/**
 * 部门路由
 * 处理部门相关的路由
 */

const express = require('express');
const router = express.Router();
const departmentController = require('../controllers/departmentController');
const { authenticateJWT, checkPermission } = require('../middlewares/auth');

// 获取所有部门
router.get('/',
    authenticateJWT,
    checkPermission('department_view'),
    departmentController.getAllDepartments
);

// 检查部门名称是否存在
router.get('/check-name',
    authenticateJWT,
    checkPermission('department_view'),
    departmentController.checkDepartmentName
);

// 根据ID获取部门
router.get('/:id',
    authenticateJWT,
    checkPermission('department_view'),
    departmentController.getDepartmentById
);

// 检查部门是否有用户关联
router.get('/:id/check-users',
    authenticateJWT,
    checkPermission('department_manage'),
    departmentController.checkDepartmentUsers
);

// 创建新部门
router.post('/',
    authenticateJWT,
    checkPermission('department_create'),
    departmentController.createDepartment
);

// 更新部门
router.put('/:id',
    authenticateJWT,
    checkPermission('department_edit'),
    departmentController.updateDepartment
);

// 删除部门
router.delete('/:id',
    authenticateJWT,
    checkPermission('department_delete'),
    departmentController.deleteDepartment
);

module.exports = router;
