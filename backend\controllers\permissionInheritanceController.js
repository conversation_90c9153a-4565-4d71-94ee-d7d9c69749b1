/**
 * 权限继承控制器
 * 处理权限继承相关的请求
 */

const { 
    resolveCompletePermissions, 
    resolvePermissionDependencies,
    resolveRolePermissionInheritance,
    validatePermissionConfiguration,
    PERMISSION_DEPENDENCIES,
    PERMISSION_COMBINATIONS,
    ROLE_PERMISSION_INHERITANCE
} = require('../config/permissionInheritance');
const logger = require('../utils/logger');

/**
 * 获取用户的完整权限（包括继承）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getUserCompletePermissions(req, res) {
    try {
        const { userId } = req.params;
        
        if (!userId) {
            return res.status(400).json({
                success: false,
                message: '用户ID不能为空'
            });
        }

        // 获取用户信息
        const db = require('../config/database');
        const stmt = db.prepare('SELECT role, permissions FROM users WHERE id = ?');
        const user = stmt.get(userId);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        const basePermissions = user.permissions ? JSON.parse(user.permissions) : [];
        const completePermissions = resolveCompletePermissions(user.role, basePermissions);

        res.json({
            success: true,
            data: {
                userId,
                role: user.role,
                basePermissions,
                completePermissions,
                inheritedPermissions: completePermissions.filter(p => !basePermissions.includes(p))
            }
        });
    } catch (error) {
        logger.error('获取用户完整权限失败:', error);
        res.status(500).json({
            success: false,
            message: '获取用户完整权限失败: ' + error.message
        });
    }
}

/**
 * 获取权限依赖关系配置
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getPermissionDependencies(req, res) {
    try {
        res.json({
            success: true,
            data: {
                dependencies: PERMISSION_DEPENDENCIES,
                combinations: PERMISSION_COMBINATIONS,
                roleInheritance: ROLE_PERMISSION_INHERITANCE
            }
        });
    } catch (error) {
        logger.error('获取权限依赖关系失败:', error);
        res.status(500).json({
            success: false,
            message: '获取权限依赖关系失败: ' + error.message
        });
    }
}

/**
 * 验证权限配置
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function validateConfiguration(req, res) {
    try {
        const validation = validatePermissionConfiguration();
        
        res.json({
            success: true,
            data: validation
        });
    } catch (error) {
        logger.error('验证权限配置失败:', error);
        res.status(500).json({
            success: false,
            message: '验证权限配置失败: ' + error.message
        });
    }
}

/**
 * 预览权限应用结果
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function previewPermissionApplication(req, res) {
    try {
        const { role, permissions } = req.body;

        if (!role || !Array.isArray(permissions)) {
            return res.status(400).json({
                success: false,
                message: '角色和权限列表不能为空'
            });
        }

        // 解析完整权限
        const completePermissions = resolveCompletePermissions(role, permissions);
        
        // 分析权限来源
        const rolePermissions = resolveRolePermissionInheritance(role, []);
        const dependencyPermissions = resolvePermissionDependencies(permissions);
        
        const analysis = {
            inputPermissions: permissions,
            roleBasedPermissions: rolePermissions.filter(p => !permissions.includes(p)),
            dependencyPermissions: dependencyPermissions.filter(p => !permissions.includes(p)),
            finalPermissions: completePermissions,
            addedPermissions: completePermissions.filter(p => !permissions.includes(p))
        };

        res.json({
            success: true,
            data: analysis
        });
    } catch (error) {
        logger.error('预览权限应用失败:', error);
        res.status(500).json({
            success: false,
            message: '预览权限应用失败: ' + error.message
        });
    }
}

/**
 * 批量更新用户权限（应用继承机制）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function batchUpdateUserPermissions(req, res) {
    try {
        const { userIds } = req.body;

        if (!Array.isArray(userIds) || userIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: '用户ID列表不能为空'
            });
        }

        const db = require('../config/database');
        const updatedUsers = [];
        const failedUsers = [];

        for (const userId of userIds) {
            try {
                // 获取用户当前信息
                const stmt = db.prepare('SELECT role, permissions FROM users WHERE id = ?');
                const user = stmt.get(userId);

                if (!user) {
                    failedUsers.push({ userId, reason: '用户不存在' });
                    continue;
                }

                const basePermissions = user.permissions ? JSON.parse(user.permissions) : [];
                const completePermissions = resolveCompletePermissions(user.role, basePermissions);

                // 更新用户权限
                const updateStmt = db.prepare('UPDATE users SET permissions = ? WHERE id = ?');
                updateStmt.run(JSON.stringify(completePermissions), userId);

                updatedUsers.push({
                    userId,
                    role: user.role,
                    oldPermissions: basePermissions,
                    newPermissions: completePermissions
                });

                logger.info('用户权限继承更新成功', { userId, role: user.role });
            } catch (error) {
                logger.error('更新用户权限失败', { userId, error: error.message });
                failedUsers.push({ userId, reason: error.message });
            }
        }

        res.json({
            success: true,
            data: {
                updatedUsers,
                failedUsers,
                summary: {
                    total: userIds.length,
                    updated: updatedUsers.length,
                    failed: failedUsers.length
                }
            }
        });
    } catch (error) {
        logger.error('批量更新用户权限失败:', error);
        res.status(500).json({
            success: false,
            message: '批量更新用户权限失败: ' + error.message
        });
    }
}

module.exports = {
    getUserCompletePermissions,
    getPermissionDependencies,
    validateConfiguration,
    previewPermissionApplication,
    batchUpdateUserPermissions
};
