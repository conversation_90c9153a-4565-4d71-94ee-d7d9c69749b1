/**
 * 用户路由
 * 处理用户相关的路由
 */

const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const { authenticateJWT, checkPermission } = require('../middlewares/auth');
const upload = require('../middlewares/upload');

// 获取所有用户
router.get('/',
    authenticateJWT,
    checkPermission('user_view'),
    userController.getAllUsers
);

// 获取厂长用户列表
router.get('/factory-managers',
    authenticateJWT,
    checkPermission('user_view'),
    userController.getFactoryManagers
);

// 获取经理用户列表
router.get('/managers',
    authenticateJWT,
    checkPermission('user_view'),
    userController.getManagers
);

// 创建新用户
router.post('/',
    authenticateJWT,
    checkPermission('user_create'),
    userController.createUser
);

// 导出用户数据
router.get('/export',
    authenticateJWT,
    checkPermission('user_export'),
    userController.exportUsers
);

// 导入用户数据
router.post('/import',
    authenticateJWT,
    checkPermission('user_import'),
    upload.single('file'),
    userController.importUsers
);

// 修改密码
router.put('/password',
    authenticateJWT,
    userController.changePassword
);

// 更新用户
router.put('/:id',
    authenticateJWT,
    checkPermission('user_edit'),
    userController.updateUser
);

// 检查用户删除状态
router.get('/:id/deletion-status',
    authenticateJWT,
    checkPermission('user_view'),
    userController.checkUserDeletionStatus
);

// 删除用户
router.delete('/:id',
    authenticateJWT,
    checkPermission('user_delete'),
    userController.deleteUser
);

// 上传用户电子签名
router.post('/:id/signature',
    authenticateJWT,
    checkPermission('user_signature'),
    upload.single('signature'),
    userController.uploadSignature
);

// 上传当前用户电子签名
router.post('/signature',
    authenticateJWT,
    upload.single('signature'),
    userController.uploadCurrentUserSignature
);

// 删除用户电子签名
router.delete('/:id/signature',
    authenticateJWT,
    checkPermission('user_signature'),
    userController.deleteSignature
);

// 获取用户电子签名
router.get('/:id/signature',
    authenticateJWT,
    checkPermission('user_view'),
    userController.getSignature
);

// 获取用户权限
router.get('/:id/permissions',
    authenticateJWT,
    checkPermission('user_permission_manage'),
    userController.getUserPermissions
);

// 更新用户权限
router.put('/:id/permissions',
    authenticateJWT,
    checkPermission('user_permission_manage'),
    userController.updateUserPermissions
);

module.exports = router;
