/**
 * 维修保养记录路由
 * 处理维修保养记录相关的路由配置
 */

const express = require('express');
const router = express.Router();
const MaintenanceController = require('../controllers/maintenanceController');
const { authenticateJWT, checkPermission } = require('../middlewares/auth');

// 创建控制器实例
const maintenanceController = new MaintenanceController();

// 应用认证中间件到所有路由
router.use(authenticateJWT);

/**
 * 获取选项数据（类型、状态等）
 * GET /api/maintenance/options
 * 权限: maintenance_view
 */
router.get('/options',
    checkPermission('maintenance_view'),
    (req, res) => {
        maintenanceController.getOptions(req, res);
    }
);

/**
 * 获取统计数据
 * GET /api/maintenance/statistics
 * 权限: maintenance_statistics
 */
router.get('/statistics',
    checkPermission('maintenance_statistics'),
    (req, res) => {
        maintenanceController.getStatistics(req, res);
    }
);

/**
 * 导出维修记录到Excel
 * GET /api/maintenance/export
 * 权限: maintenance_export
 */
router.get('/export',
    checkPermission('maintenance_export'),
    (req, res) => {
        maintenanceController.exportMaintenanceRecordsToExcel(req, res);
    }
);

/**
 * 批量导入维修记录
 * POST /api/maintenance/import
 * 权限: maintenance_import
 */
router.post('/import',
    checkPermission('maintenance_import'),
    (req, res) => {
        maintenanceController.importMaintenanceRecords(req, res);
    }
);

/**
 * 获取维修记录列表
 * GET /api/maintenance
 * 权限: maintenance_view
 *
 * 查询参数：
 * - page: 页码
 * - limit: 每页数量
 * - search: 搜索关键词
 * - equipmentId: 设备ID筛选
 * - type: 类型筛选
 * - status: 状态筛选
 * - technician: 技术员筛选
 * - area: 厂区筛选
 * - startDate: 开始时间筛选
 * - endDate: 结束时间筛选
 */
router.get('/',
    checkPermission('maintenance_view'),
    (req, res) => {
        maintenanceController.getMaintenanceRecords(req, res);
    }
);

/**
 * 创建维修记录
 * POST /api/maintenance
 * 权限: maintenance_create
 *
 * 请求体：
 * {
 *   "equipmentId": "设备ID",
 *   "type": "maintenance|repair",
 *   "description": "描述",
 *   "startDate": "开始时间",
 *   "endDate": "结束时间（可选）",
 *   "cost": "费用（可选）",
 *   "technician": "技术员",
 *   "status": "状态（可选）",
 *   "notes": "备注（可选）"
 * }
 */
router.post('/',
    checkPermission('maintenance_create'),
    (req, res) => {
        maintenanceController.createMaintenanceRecord(req, res);
    }
);

/**
 * 获取单个维修记录详情
 * GET /api/maintenance/:id
 * 权限: maintenance_view
 */
router.get('/:id',
    checkPermission('maintenance_view'),
    (req, res) => {
        maintenanceController.getMaintenanceRecordById(req, res);
    }
);

/**
 * 更新维修记录
 * PUT /api/maintenance/:id
 * 权限: maintenance_edit
 *
 * 请求体：与创建时相同，所有字段都是可选的
 */
router.put('/:id',
    checkPermission('maintenance_edit'),
    (req, res) => {
        maintenanceController.updateMaintenanceRecord(req, res);
    }
);

/**
 * 删除维修记录
 * DELETE /api/maintenance/:id
 * 权限: maintenance_delete
 */
router.delete('/:id',
    checkPermission('maintenance_delete'),
    (req, res) => {
        maintenanceController.deleteMaintenanceRecord(req, res);
    }
);

module.exports = router;
