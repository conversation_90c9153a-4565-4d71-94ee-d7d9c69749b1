/**
 * 用户控制器
 * 处理用户相关的请求
 */

const userService = require('../services/userService');
const fs = require('fs');
const path = require('path');
const config = require('../config');
const logger = require('../utils/logger');

/**
 * 获取所有用户
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getAllUsers(req, res) {
    try {
        // 检查权限 - 只有admin角色才能访问用户管理
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '没有权限访问用户列表，只有系统管理员才能访问'
            });
        }

        // 获取查询参数
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const search = req.query.search || '';
        const role = req.query.role || '';
        const department = req.query.department || '';
        const status = req.query.status || '';

        const users = userService.readUsers();

        // 过滤用户
        let filteredUsers = users;

        // 搜索过滤
        if (search) {
            const searchLower = search.toLowerCase();
            filteredUsers = filteredUsers.filter(user =>
                (user.usercode && user.usercode.toLowerCase().includes(searchLower)) ||
                (user.username && user.username.toLowerCase().includes(searchLower)) ||
                (user.email && user.email.toLowerCase().includes(searchLower))
            );
        }

        // 角色过滤
        if (role) {
            filteredUsers = filteredUsers.filter(user => user.role === role);
        }

        // 部门过滤
        if (department) {
            filteredUsers = filteredUsers.filter(user => user.department === department);
        }

        // 状态过滤
        if (status === 'active') {
            filteredUsers = filteredUsers.filter(user => user.active === true);
        } else if (status === 'inactive') {
            filteredUsers = filteredUsers.filter(user => user.active === false);
        }

        // 计算总数和分页
        const total = filteredUsers.length;
        const totalPages = Math.ceil(total / limit);
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

        // 不返回密码，将usercode映射为code，username映射为name
        const usersWithoutPassword = paginatedUsers.map(user => {
            const { password, ...userWithoutPassword } = user;
            return {
                ...userWithoutPassword,
                // 将usercode映射为code
                code: user.usercode,
                // 将username映射为name
                name: user.username,
                // 格式化创建日期
                createdAt: formatDate(user.createdAt),
                // 确保包含签名信息
                hasSignature: user.hasSignature || false,
                signaturePath: user.signaturePath || null
            };
        });

        res.json({
            users: usersWithoutPassword,
            pagination: {
                total,
                totalPages,
                currentPage: page,
                limit
            }
        });
    } catch (error) {
        console.error('获取用户列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取用户列表失败: ' + error.message
        });
    }
}

/**
 * 创建新用户
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function createUser(req, res) {
    try {
        // 检查权限 - 只有admin角色才能创建用户
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '没有权限创建用户，只有系统管理员才能创建用户'
            });
        }

        const { code, name, email, role, department, password, active } = req.body;

        // 验证必填字段
        if (!name || !role || !password) {
            return res.status(400).json({
                success: false,
                message: '缺少必填字段'
            });
        }

        // 创建用户
        const newUser = userService.createUser({
            usercode: code || generateUsername(name),
            username: name,
            password,
            email,
            role,
            department,
            active: active !== undefined ? active : true
        });

        res.status(201).json({
            success: true,
            user: {
                ...newUser,
                code: newUser.usercode,
                name: newUser.username,
                createdAt: formatDate(newUser.createdAt)
            }
        });
    } catch (error) {
        console.error('创建用户失败:', error);
        res.status(500).json({
            success: false,
            message: '创建用户失败: ' + error.message
        });
    }
}

/**
 * 更新用户
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function updateUser(req, res) {
    try {
        // 检查权限 - 只有admin角色才能更新用户
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '没有权限更新用户，只有系统管理员才能更新用户'
            });
        }

        const userId = req.params.id;
        const { code, name, email, role, department, password, active } = req.body;

        // 验证必填字段
        if (!name || !role) {
            return res.status(400).json({
                success: false,
                message: '缺少必填字段'
            });
        }

        // 获取用户
        const user = userService.getUserById(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        // 更新用户
        const updatedUser = {
            ...user,
            usercode: code || user.usercode,
            username: name,
            email: email || user.email,
            role,
            department: department || user.department,
            active: active !== undefined ? active : user.active
        };

        // 如果提供了密码，则更新密码
        if (password) {
            updatedUser.password = userService.hashPassword(password);
        }

        // 直接保存更新后的用户文件
        userService.writeUserFile(updatedUser);

        // 不返回密码
        const { password: _, ...userWithoutPassword } = updatedUser;

        res.json({
            success: true,
            user: {
                ...userWithoutPassword,
                code: updatedUser.usercode,
                name: updatedUser.username,
                createdAt: formatDate(userWithoutPassword.createdAt)
            }
        });
    } catch (error) {
        console.error('更新用户失败:', error);
        res.status(500).json({
            success: false,
            message: '更新用户失败: ' + error.message
        });
    }
}

/**
 * 删除用户
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function deleteUser(req, res) {
    try {
        // 检查权限 - 只有admin角色才能删除用户
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '没有权限删除用户，只有系统管理员才能删除用户'
            });
        }

        const userId = req.params.id;

        // 获取用户
        const user = userService.getUserById(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        // 不允许删除自己
        if (userId === req.user.id) {
            return res.status(400).json({
                success: false,
                message: '不能删除当前登录的用户'
            });
        }

        // 检查是否为最后一个管理员
        if (user.role === 'admin') {
            const adminCount = userService.getAdminCount();
            if (adminCount <= 1) {
                return res.status(400).json({
                    success: false,
                    message: '不能删除最后一个管理员账户，系统至少需要保留一个管理员'
                });
            }
        }

        // 检查是否有未完成的重要业务
        const businessCheck = userService.checkUserActiveBusiness(userId);
        if (businessCheck.hasActiveBusiness) {
            let errorMessage = '该用户有未完成的重要业务，请先处理完成后再删除：';
            const details = [];
            if (businessCheck.pendingApplications > 0) {
                details.push(`${businessCheck.pendingApplications} 个未完成的申请`);
            }
            if (businessCheck.pendingApprovals > 0) {
                details.push(`${businessCheck.pendingApprovals} 个待审批的申请`);
            }
            if (businessCheck.recentReports > 0) {
                details.push(`${businessCheck.recentReports} 个最近上传的质量报告`);
            }
            if (businessCheck.error) {
                details.push(`业务检查出现错误: ${businessCheck.error}`);
            }

            return res.status(400).json({
                success: false,
                message: errorMessage,
                details: details,
                businessCheck: businessCheck
            });
        }

        // 删除用户及其关联数据
        const result = userService.deleteUserFile(userId);

        if (result) {
            // 记录业务事件
            logger.logBusinessEvent('用户管理', '删除用户', {
                deletedUserId: userId,
                deletedUserCode: user.usercode,
                deletedUserName: user.username
            }, req.user);

            res.json({
                success: true,
                message: '用户删除成功'
            });
        } else {
            res.status(500).json({
                success: false,
                message: '删除用户失败'
            });
        }
    } catch (error) {
        logger.error('删除用户失败:', error);
        res.status(500).json({
            success: false,
            message: '删除用户失败: ' + error.message
        });
    }
}

/**
 * 检查用户删除前的业务状态
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function checkUserDeletionStatus(req, res) {
    try {
        // 检查权限 - 只有admin角色才能检查
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '没有权限执行此操作'
            });
        }

        const userId = req.params.id;

        // 获取用户
        const user = userService.getUserById(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        // 检查是否为最后一个管理员
        let isLastAdmin = false;
        if (user.role === 'admin') {
            const adminCount = userService.getAdminCount();
            isLastAdmin = adminCount <= 1;
        }

        // 检查活跃业务
        const businessCheck = userService.checkUserActiveBusiness(userId);

        res.json({
            success: true,
            canDelete: !isLastAdmin && !businessCheck.hasActiveBusiness,
            isLastAdmin,
            businessCheck,
            user: {
                id: user.id,
                username: user.username,
                role: user.role
            }
        });
    } catch (error) {
        logger.error('检查用户删除状态失败:', error);
        res.status(500).json({
            success: false,
            message: '检查用户删除状态失败: ' + error.message
        });
    }
}

/**
 * 导出用户数据为CSV
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function exportUsers(req, res) {
    try {
        // 检查权限 - 只有admin角色才能导出用户数据
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '没有权限导出用户数据，只有系统管理员才能导出用户数据'
            });
        }

        const users = userService.readUsers();

        // 生成CSV内容
        const csvHeader = '用户代码,姓名,角色,部门,邮箱,状态,创建时间\n';
        const csvRows = users.map(user => {
            return `${user.usercode},${user.username},${user.role},${user.department || ''},${user.email || ''},${user.active ? '启用' : '禁用'},${formatDate(user.createdAt)}`;
        }).join('\n');

        // 添加BOM标记以支持中文
        const BOM = '\uFEFF';
        const csvContent = BOM + csvHeader + csvRows;

        // 设置响应头
        res.setHeader('Content-Type', 'text/csv; charset=utf-8');
        res.setHeader('Content-Disposition', 'attachment; filename=users.csv');

        // 发送CSV内容
        res.send(csvContent);
    } catch (error) {
        console.error('导出用户数据失败:', error);
        res.status(500).json({
            success: false,
            message: '导出用户数据失败: ' + error.message
        });
    }
}

/**
 * 导入CSV用户数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function importUsers(req, res) {
    try {
        // 检查权限 - 只有admin角色才能导入用户数据
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '没有权限导入用户数据，只有系统管理员才能导入用户数据'
            });
        }

        // 检查是否有文件上传
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: '没有上传文件'
            });
        }

        // 读取CSV文件内容
        let fileContent = fs.readFileSync(req.file.path, 'utf8');

        // 移除UTF-8 BOM标记（如果存在）
        if (fileContent.charCodeAt(0) === 0xFEFF) {
            fileContent = fileContent.substring(1);
        }

        const lines = fileContent.split('\n');

        // 跳过标题行
        const dataRows = lines.slice(1).filter(line => line.trim());

        // 解析CSV数据
        const users = userService.readUsers();
        let importedCount = 0;

        for (const row of dataRows) {
            // 简单分割CSV行
            const columns = row.split(',').map(item => item.trim());

            // 确保至少有必要的列
            if (columns.length < 5) continue;

            const [code, name, role, department, email, status] = columns;

            // 检查必填字段
            if (!code || !name || !role) continue;

            // 检查用户是否已存在（通过usercode）
            const existingUser = users.find(u => u.usercode === code);

            if (existingUser) {
                // 更新现有用户
                existingUser.username = name;
                existingUser.role = role;
                existingUser.department = department;
                existingUser.email = email;
                existingUser.active = status === '启用';
                // 直接保存更新后的用户
                userService.writeUserFile(existingUser);
            } else {
                // 创建新用户
                const newUser = {
                    id: userService.generateId(),
                    usercode: code,
                    username: name,
                    password: userService.hashPassword('password123'), // 默认密码
                    role,
                    department,
                    email,
                    active: status === '启用',
                    createdAt: new Date().toISOString()
                };
                // 直接保存到单独文件
                userService.writeUserFile(newUser);
            }

            importedCount++;
        }

        // 删除临时文件
        fs.unlinkSync(req.file.path);

        res.json({
            success: true,
            message: `成功导入 ${importedCount} 条用户数据`,
            importedCount
        });
    } catch (error) {
        console.error('导入用户数据失败:', error);
        console.error('错误堆栈:', error.stack);
        console.error('请求文件:', req.file);
        console.error('请求头:', req.headers);

        // 如果文件存在，删除临时文件
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }

        res.status(500).json({
            success: false,
            message: '导入用户数据失败: ' + error.message
        });
    }
}

/**
 * 格式化日期
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期
 */
function formatDate(dateString) {
    if (!dateString) return '';

    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    } catch (error) {
        return dateString;
    }
}

/**
 * 生成用户名
 * @param {string} name - 用户姓名
 * @returns {string} 生成的用户名
 */
function generateUsername(name) {
    const timestamp = Date.now().toString().slice(-6);
    return `user${timestamp}`;
}

/**
 * 上传用户电子签名
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function uploadSignature(req, res) {
    try {
        // 检查权限 - 只有admin角色才能上传电子签名
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '没有权限上传电子签名，只有系统管理员才能上传电子签名'
            });
        }

        const userId = req.params.id;

        // 验证用户存在
        const user = userService.getUserById(userId);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        // 验证是否有文件上传
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: '未提供签名文件'
            });
        }

        // 获取文件信息
        const signatureFile = req.file;
        const fileExtension = path.extname(signatureFile.originalname).toLowerCase();

        // 验证文件类型（只允许JPG和PNG）
        const allowedExtensions = ['.jpg', '.jpeg', '.png'];
        if (!allowedExtensions.includes(fileExtension)) {
            // 删除上传的文件
            fs.unlinkSync(signatureFile.path);

            return res.status(400).json({
                success: false,
                message: '签名文件格式不正确，只允许JPG和PNG格式'
            });
        }

        // 设置签名文件的新路径和文件名
        const signatureDir = path.join(config.paths.uploads, 'signatures');

        // 确保目录存在
        if (!fs.existsSync(signatureDir)) {
            fs.mkdirSync(signatureDir, { recursive: true });
        }

        // 获取当前日期作为签名文件名的一部分
        const now = new Date();
        const dateStr = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}`;

        // 使用用户代码(usercode)而不是用户ID来命名文件
        const userCode = user.usercode;
        // 使用用户代码作为文件名前缀
        const signatureFileName = `${userCode}_${dateStr}${fileExtension}`;
        const signaturePath = path.join(signatureDir, signatureFileName);

        // 如果用户已有签名，则删除旧签名
        if (user.signaturePath) {
            const oldSignaturePath = path.join(config.paths.base, user.signaturePath);
            if (fs.existsSync(oldSignaturePath)) {
                fs.unlinkSync(oldSignaturePath);
            }
        }

        // 移动上传的文件到目标位置
        fs.copyFileSync(signatureFile.path, signaturePath);
        fs.unlinkSync(signatureFile.path); // 删除临时文件

        // 更新用户签名信息
        const relativePath = path.join('uploads', 'signatures', signatureFileName).replace(/\\/g, '/');
        user.signaturePath = relativePath;
        user.hasSignature = true;

        // 保存用户数据
        userService.writeUserFile(user);

        res.json({
            success: true,
            message: '电子签名上传成功',
            signaturePath: relativePath
        });
    } catch (error) {
        console.error('上传电子签名失败:', error);
        res.status(500).json({
            success: false,
            message: '上传电子签名失败: ' + error.message
        });
    }
}

/**
 * 删除用户电子签名
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function deleteSignature(req, res) {
    try {
        // 检查权限 - 只有admin角色才能删除电子签名
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '没有权限删除电子签名，只有系统管理员才能删除电子签名'
            });
        }

        const userId = req.params.id;

        // 验证用户存在
        const user = userService.getUserById(userId);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        // 验证用户是否有签名
        if (!user.signaturePath) {
            return res.status(400).json({
                success: false,
                message: '用户没有电子签名'
            });
        }

        // 删除签名文件
        const signaturePath = path.join(config.paths.base, user.signaturePath);
        if (fs.existsSync(signaturePath)) {
            fs.unlinkSync(signaturePath);
        }

        // 更新用户信息
        user.signaturePath = null;
        user.hasSignature = false;

        // 保存用户数据
        userService.writeUserFile(user);

        res.json({
            success: true,
            message: '电子签名删除成功'
        });
    } catch (error) {
        console.error('删除电子签名失败:', error);
        res.status(500).json({
            success: false,
            message: '删除电子签名失败: ' + error.message
        });
    }
}

/**
 * 获取用户电子签名
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getSignature(req, res) {
    try {
        const userId = req.params.id;

        // 验证用户存在
        const user = userService.getUserById(userId);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        // 检查用户是否有签名
        if (!user.signaturePath) {
            // 返回200状态码，但指明用户没有签名
            return res.json({
                success: true,
                hasSignature: false,
                message: '用户没有电子签名'
            });
        }

        res.json({
            success: true,
            signaturePath: user.signaturePath,
            hasSignature: true
        });
    } catch (error) {
        console.error('获取电子签名失败:', error);
        res.status(500).json({
            success: false,
            message: '获取电子签名失败: ' + error.message
        });
    }
}

/**
 * 上传当前用户电子签名
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function uploadCurrentUserSignature(req, res) {
    try {
        // 获取当前用户ID
        const userId = req.user.id;

        // 验证用户存在
        const user = userService.getUserById(userId);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        // 验证是否有文件上传
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: '未提供签名文件'
            });
        }

        // 获取文件信息
        const signatureFile = req.file;
        const fileExtension = path.extname(signatureFile.originalname).toLowerCase();

        // 验证文件类型（只允许JPG和PNG）
        const allowedExtensions = ['.jpg', '.jpeg', '.png'];
        if (!allowedExtensions.includes(fileExtension)) {
            // 删除上传的文件
            fs.unlinkSync(signatureFile.path);

            return res.status(400).json({
                success: false,
                message: '签名文件格式不正确，只允许JPG和PNG格式'
            });
        }

        // 设置签名文件的新路径和文件名
        const signatureDir = path.join(config.paths.uploads, 'signatures');

        // 确保目录存在
        if (!fs.existsSync(signatureDir)) {
            fs.mkdirSync(signatureDir, { recursive: true });
        }

        // 获取当前日期作为签名文件名的一部分
        const now = new Date();
        const dateStr = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}`;

        // 使用用户代码(usercode)而不是用户ID来命名文件
        const userCode = user.usercode;
        // 使用用户代码作为文件名前缀
        const signatureFileName = `${userCode}_${dateStr}${fileExtension}`;
        const signaturePath = path.join(signatureDir, signatureFileName);

        // 如果用户已有签名，则删除旧签名
        if (user.signaturePath) {
            const oldSignaturePath = path.join(config.paths.base, user.signaturePath);
            if (fs.existsSync(oldSignaturePath)) {
                fs.unlinkSync(oldSignaturePath);
            }
        }

        // 移动上传的文件到目标位置
        fs.copyFileSync(signatureFile.path, signaturePath);
        fs.unlinkSync(signatureFile.path); // 删除临时文件

        // 更新用户签名信息
        const relativePath = path.join('uploads', 'signatures', signatureFileName).replace(/\\/g, '/');
        user.signaturePath = relativePath;
        user.hasSignature = true;

        // 保存用户数据
        userService.writeUserFile(user);

        res.json({
            success: true,
            message: '电子签名上传成功',
            signaturePath: relativePath
        });
    } catch (error) {
        console.error('上传电子签名失败:', error);
        res.status(500).json({
            success: false,
            message: '上传电子签名失败: ' + error.message
        });
    }
}

/**
 * 获取厂长用户列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getFactoryManagers(req, res) {
    try {
        logger.debug('获取厂长用户列表');

        // 获取所有用户
        const users = userService.readUsers();

        // 筛选出厂长角色的用户
        const factoryManagers = users.filter(user =>
            user.role === '厂长' && user.active === true
        ).map(user => {
            // 不返回密码等敏感信息
            const { password, ...userWithoutPassword } = user;
            return {
                ...userWithoutPassword,
                code: user.usercode,
                name: user.username
            };
        });

        logger.debug(`找到 ${factoryManagers.length} 个厂长用户`);

        res.json({
            success: true,
            factoryManagers
        });
    } catch (error) {
        logger.error('获取厂长用户列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取厂长用户列表失败: ' + error.message
        });
    }
}

/**
 * 获取经理用户列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getManagers(req, res) {
    try {
        logger.debug('获取经理用户列表');

        // 获取所有用户
        const users = userService.readUsers();

        // 筛选出经理角色的用户
        const managers = users.filter(user =>
            user.role === '经理' && user.active === true
        ).map(user => {
            // 不返回密码等敏感信息
            const { password, ...userWithoutPassword } = user;
            return {
                ...userWithoutPassword,
                code: user.usercode,
                name: user.username
            };
        });

        logger.debug(`找到 ${managers.length} 个经理用户`);

        res.json({
            success: true,
            managers
        });
    } catch (error) {
        logger.error('获取经理用户列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取经理用户列表失败: ' + error.message
        });
    }
}

/**
 * 获取用户列表（用于邮件通知选择）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getUsersForNotification(req, res) {
    try {
        logger.debug('获取用户列表用于邮件通知选择');

        // 获取所有用户
        const users = userService.readUsers();

        // 筛选出活跃且有邮箱的用户
        const notificationUsers = users.filter(user =>
            user.active === true && user.email && user.email.trim() !== ''
        ).map(user => {
            // 不返回密码等敏感信息
            const { password, ...userWithoutPassword } = user;
            return {
                ...userWithoutPassword,
                code: user.usercode,
                name: user.username
            };
        });

        logger.debug(`找到 ${notificationUsers.length} 个可通知用户`);

        res.json({
            success: true,
            data: notificationUsers
        });
    } catch (error) {
        logger.error('获取通知用户列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取通知用户列表失败: ' + error.message
        });
    }
}

/**
 * 获取用户权限
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getUserPermissions(req, res) {
    try {
        logger.debug(`获取用户权限，用户ID: ${req.params.id}`);

        // 检查权限 - 只有admin角色才能查看用户权限
        if (req.user.role !== 'admin') {
            logger.debug(`权限不足，当前用户角色: ${req.user.role}`);
            return res.status(403).json({
                success: false,
                message: '没有权限查看用户权限，只有系统管理员才能查看用户权限'
            });
        }

        const userId = req.params.id;
        logger.debug(`处理用户ID: ${userId}`);

        // 验证用户存在
        const user = userService.getUserById(userId);

        if (!user) {
            logger.debug(`用户不存在: ${userId}`);
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        logger.debug(`找到用户: ${user.username}, ID: ${user.id}`);

        // 获取用户的基础权限（存储在数据库中的权限）
        let basePermissions = user.permissions || [];

        // 对于权限管理界面，返回基础权限用于编辑
        // 这样用户看到的是他们实际选择的权限，而不是继承的权限
        let permissions = basePermissions;

        // 如果是管理员用户且基础权限为空，初始化为所有权限
        if (user.role === 'admin' && basePermissions.length === 0) {
            const { resolveCompletePermissions } = require('../config/permissionInheritance');
            const completePermissions = resolveCompletePermissions(user.role, []);

            // 更新数据库中的基础权限
            user.permissions = completePermissions;
            userService.writeUserFile(user);
            permissions = completePermissions;

            logger.debug(`初始化管理员用户权限，权限数量: ${completePermissions.length}`);
        }

        // 返回用户权限
        res.json({
            success: true,
            permissions: permissions
        });
    } catch (error) {
        logger.error('获取用户权限失败:', error);
        res.status(500).json({
            success: false,
            message: '获取用户权限失败: ' + error.message
        });
    }
}



/**
 * 更新用户权限
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function updateUserPermissions(req, res) {
    try {
        logger.debug(`开始更新用户权限，用户ID: ${req.params.id}`);

        // 检查权限 - 只有admin角色才能管理用户权限
        if (req.user.role !== 'admin') {
            logger.debug(`权限不足，当前用户角色: ${req.user.role}`);
            return res.status(403).json({
                success: false,
                message: '没有权限更新用户权限'
            });
        }

        const userId = req.params.id;
        logger.debug(`处理用户ID: ${userId}`);

        const { permissions } = req.body;
        logger.debug(`接收到的权限数据: ${JSON.stringify(permissions)}`);

        if (!Array.isArray(permissions)) {
            logger.debug('权限数据不是数组');
            return res.status(400).json({
                success: false,
                message: '权限必须是数组'
            });
        }

        // 验证用户存在
        logger.debug(`尝试获取用户: ${userId}`);
        const user = userService.getUserById(userId);

        if (!user) {
            logger.debug(`用户不存在: ${userId}`);
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        logger.debug(`找到用户: ${user.username}, ID: ${user.id}`);

        // 确保用户对象有permissions字段
        if (!user.permissions) {
            user.permissions = [];
        }

        // 更新用户的基础权限（存储用户实际选择的权限）
        user.permissions = permissions;
        logger.debug(`更新后的基础权限: ${JSON.stringify(user.permissions)}`);

        // 注意：这里只存储基础权限，权限继承在运行时处理
        // 这样确保前端显示的权限与用户选择的权限一致

        // 保存用户数据
        const saveResult = userService.writeUserFile(user);
        logger.debug(`保存用户数据结果: ${saveResult ? '成功' : '失败'}`);

        if (!saveResult) {
            return res.status(500).json({
                success: false,
                message: '保存用户权限失败'
            });
        }

        // 不返回密码
        const { password: _, ...userWithoutPassword } = user;

        logger.debug(`用户权限更新成功: ${user.username}`);

        // 记录业务事件
        logger.logBusinessEvent('用户管理', '更新权限', {
            userId: user.id,
            username: user.username,
            permissions: permissions
        }, req.user);

        res.json({
            success: true,
            message: '用户权限更新成功',
            user: {
                ...userWithoutPassword,
                code: user.usercode,
                name: user.username
            }
        });
    } catch (error) {
        logger.error('更新用户权限失败:', error);
        res.status(500).json({
            success: false,
            message: '更新用户权限失败: ' + error.message
        });
    }
}

/**
 * 修改用户密码
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function changePassword(req, res) {
    try {
        // 获取请求参数
        const { currentPassword, newPassword } = req.body;

        // 验证必填字段
        if (!currentPassword || !newPassword) {
            return res.status(400).json({
                success: false,
                message: '当前密码和新密码不能为空'
            });
        }

        // 验证新密码长度
        if (newPassword.length < 6) {
            return res.status(400).json({
                success: false,
                message: '新密码长度至少为6个字符'
            });
        }

        // 获取当前用户
        const userId = req.user.id;
        const user = userService.getUserById(userId);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        // 验证当前密码
        const bcrypt = require('bcryptjs');
        const isPasswordValid = bcrypt.compareSync(currentPassword, user.password);

        if (!isPasswordValid) {
            return res.status(401).json({
                success: false,
                message: '当前密码不正确'
            });
        }

        // 更新密码
        user.password = userService.hashPassword(newPassword);

        // 保存用户数据
        const saveResult = userService.writeUserFile(user);

        if (!saveResult) {
            return res.status(500).json({
                success: false,
                message: '保存密码失败'
            });
        }

        // 记录业务事件
        logger.logBusinessEvent('用户管理', '修改密码', {
            userId: user.id,
            username: user.username
        }, req.user);

        // 返回成功响应，并指示前端需要重新登录
        res.json({
            success: true,
            message: '密码修改成功',
            requireRelogin: true
        });
    } catch (error) {
        logger.error('修改密码失败:', error);
        res.status(500).json({
            success: false,
            message: '修改密码失败: ' + error.message
        });
    }
}

module.exports = {
    getAllUsers,
    createUser,
    updateUser,
    deleteUser,
    checkUserDeletionStatus,
    exportUsers,
    importUsers,
    uploadSignature,
    uploadCurrentUserSignature,
    deleteSignature,
    getSignature,
    getFactoryManagers,
    getManagers,
    getUsersForNotification,
    getUserPermissions,
    updateUserPermissions,
    changePassword
};
