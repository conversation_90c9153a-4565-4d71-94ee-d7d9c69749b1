/**
 * 认证控制器
 * 处理用户认证相关的请求
 */

const userService = require('../services/userService');

/**
 * 用户登录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function login(req, res) {
    try {
        const { username, password } = req.body;

        if (!username || !password) {
            return res.status(400).json({
                success: false,
                message: '用户名和密码不能为空'
            });
        }

        try {
            const user = userService.authenticateUser(username, password);

            if (!user) {
                return res.status(401).json({
                    success: false,
                    message: '用户名或密码错误'
                });
            }

            const token = userService.generateToken(user);

            res.json({
                success: true,
                token,
                user
            });
        } catch (authError) {
            // 处理特定的认证错误，如账户被禁用
            console.error('认证错误:', authError);
            return res.status(403).json({
                success: false,
                message: authError.message
            });
        }
    } catch (error) {
        console.error('登录系统错误:', error);
        res.status(500).json({
            success: false,
            message: '系统错误，请稍后再试'
        });
    }
}

/**
 * 获取当前用户信息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getCurrentUser(req, res) {
    try {
        const user = userService.getUserById(req.user.id);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        // 不返回密码
        const { password, ...userWithoutPassword } = user;

        // 只有admin角色拥有所有权限
        let permissions = user.permissions || [];
        const isSystemAdmin = user.role === 'admin';

        if (isSystemAdmin) {
            // 系统管理员的完整权限列表 - 与auth.js保持一致
            const adminPermissions = [
                // 申请管理模块
                'application_view', 'application_create', 'application_edit', 'application_delete',
                'application_approve', 'application_reject', 'application_download',

                // 生产排程模块
                'schedule_view', 'schedule_create', 'schedule_edit', 'schedule_delete',
                'schedule_execute', 'schedule_report', 'scheduling_algorithm', 'resource_manage',

                // 设备管理模块
                'equipment_view', 'equipment_create', 'equipment_edit', 'equipment_delete',
                'equipment_maintenance', 'equipment_health', 'equipment_capacity', 'equipment_export',

                // 维护管理模块
                'maintenance_view', 'maintenance_create', 'maintenance_edit', 'maintenance_delete',
                'maintenance_export', 'maintenance_import', 'maintenance_statistics',

                // 质量管理模块
                'quality_view', 'quality_upload', 'quality_edit', 'quality_delete',
                'quality_download', 'quality_manage',

                // 文件管理模块
                'file_view', 'file_upload', 'file_download', 'file_edit',
                'file_delete', 'file_confirm', 'file_manage',

                // 产品管理模块
                'product_view', 'product_create', 'product_edit', 'product_delete', 'product_manage',

                // 用户管理模块
                'user_view', 'user_create', 'user_edit', 'user_delete',
                'user_export', 'user_import', 'user_signature', 'user_password_reset', 'user_permission_manage',

                // 部门管理模块
                'department_view', 'department_create', 'department_edit', 'department_delete', 'department_manage',

                // 权限管理模块
                'permission_view', 'permission_assign', 'permission_template', 'role_manage',

                // 系统管理模块
                'system_config', 'system_monitor', 'system_backup', 'log_view', 'log_download', 'log_manage',

                // 算法调优模块
                'tuning_view', 'tuning_edit', 'tuning_execute', 'algorithm_manage',

                // 报表统计模块
                'report_view', 'report_create', 'report_export', 'statistics_view',

                // 通用权限
                'user_settings', 'dashboard_view'
            ];

            // 合并权限并去重
            permissions = [...new Set([...permissions, ...adminPermissions])];
        }

        // 添加code和name字段
        const userWithCode = {
            ...userWithoutPassword,
            code: user.usercode,
            name: user.username,
            permissions: permissions
        };

        res.json(userWithCode);
    } catch (error) {
        console.error('获取用户信息失败:', error);
        res.status(500).json({
            success: false,
            message: '获取用户信息失败: ' + error.message
        });
    }
}

/**
 * 创建新用户
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function register(req, res) {
    try {
        const { username, password, department } = req.body;

        if (!username || !password) {
            return res.status(400).json({
                success: false,
                message: '用户名和密码不能为空'
            });
        }

        // 生成用户代码
        const usercode = generateUsername(username);

        const newUser = userService.createUser({
            usercode,
            username,
            password,
            department: department || '',
            role: 'user'
        });

        // 添加code和name字段
        const userWithCode = {
            ...newUser,
            code: usercode,
            name: newUser.username
        };

        res.status(201).json({
            success: true,
            user: userWithCode
        });
    } catch (error) {
        console.error('注册失败:', error);
        res.status(500).json({
            success: false,
            message: '注册失败: ' + error.message
        });
    }
}

/**
 * 生成用户名
 * @param {string} name - 用户姓名
 * @returns {string} 生成的用户名
 */
function generateUsername(name) {
    const timestamp = Date.now().toString().slice(-6);
    return `user${timestamp}`;
}

module.exports = {
    login,
    getCurrentUser,
    register
};
