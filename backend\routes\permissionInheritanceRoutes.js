/**
 * 权限继承路由
 * 处理权限继承相关的API请求
 */

const express = require('express');
const router = express.Router();
const permissionInheritanceController = require('../controllers/permissionInheritanceController');
const { authenticateJWT, checkPermission } = require('../middlewares/auth');

// 获取用户完整权限（包括继承）
router.get('/users/:userId/complete-permissions', 
    authenticateJWT, 
    checkPermission('permission_view'), 
    permissionInheritanceController.getUserCompletePermissions
);

// 获取权限依赖关系配置
router.get('/dependencies', 
    authenticateJWT, 
    checkPermission('permission_view'), 
    permissionInheritanceController.getPermissionDependencies
);

// 验证权限配置
router.get('/validate', 
    authenticateJWT, 
    checkPermission('system_config'), 
    permissionInheritanceController.validateConfiguration
);

// 预览权限应用结果
router.post('/preview', 
    authenticateJWT, 
    checkPermission('permission_view'), 
    permissionInheritanceController.previewPermissionApplication
);

// 批量更新用户权限（应用继承机制）
router.post('/batch-update', 
    authenticateJWT, 
    checkPermission('permission_assign'), 
    permissionInheritanceController.batchUpdateUserPermissions
);

module.exports = router;
