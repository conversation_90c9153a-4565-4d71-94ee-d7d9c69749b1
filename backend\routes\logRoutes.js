/**
 * 日志路由
 * 处理日志相关的路由
 */

const express = require('express');
const router = express.Router();
const { authenticateJWT, checkPermission } = require('../middlewares/auth');
const logViewer = require('../utils/logViewer');
const logger = require('../utils/logger');

// 获取所有日志文件列表
router.get('/files', authenticateJWT, checkPermission('log_view'), (req, res) => {
    try {
        const files = logViewer.getLogFiles();
        res.json(files);
    } catch (error) {
        logger.error('获取日志文件列表失败', { error: error.message, stack: error.stack });
        res.status(500).json({
            success: false,
            message: '获取日志文件列表失败: ' + error.message
        });
    }
});

// 读取日志文件内容
router.get('/files/:fileName', authenticateJWT, checkPermission('log_view'), (req, res) => {
    try {
        const { fileName } = req.params;
        const limit = parseInt(req.query.limit) || 100;
        const skip = parseInt(req.query.skip) || 0;
        
        const logs = logViewer.readLogFile(fileName, limit, skip);
        res.json(logs);
    } catch (error) {
        logger.error('读取日志文件失败', { error: error.message, stack: error.stack, fileName: req.params.fileName });
        res.status(500).json({
            success: false,
            message: '读取日志文件失败: ' + error.message
        });
    }
});

// 搜索日志文件
router.get('/search/:fileName', authenticateJWT, checkPermission('log_view'), (req, res) => {
    try {
        const { fileName } = req.params;
        const { term } = req.query;
        const limit = parseInt(req.query.limit) || 100;
        
        if (!term) {
            return res.status(400).json({
                success: false,
                message: '搜索关键词不能为空'
            });
        }
        
        const logs = logViewer.searchLogFile(fileName, term, limit);
        res.json(logs);
    } catch (error) {
        logger.error('搜索日志文件失败', { 
            error: error.message, 
            stack: error.stack, 
            fileName: req.params.fileName,
            term: req.query.term
        });
        res.status(500).json({
            success: false,
            message: '搜索日志文件失败: ' + error.message
        });
    }
});

// 删除日志文件
router.delete('/files/:fileName', authenticateJWT, checkPermission('log_manage'), (req, res) => {
    try {
        const { fileName } = req.params;
        
        const success = logViewer.deleteLogFile(fileName);
        
        if (success) {
            logger.info('删除日志文件成功', { fileName, user: req.user });
            res.json({
                success: true,
                message: '删除日志文件成功'
            });
        } else {
            res.status(500).json({
                success: false,
                message: '删除日志文件失败'
            });
        }
    } catch (error) {
        logger.error('删除日志文件失败', { error: error.message, stack: error.stack, fileName: req.params.fileName });
        res.status(500).json({
            success: false,
            message: '删除日志文件失败: ' + error.message
        });
    }
});

// 清理过期日志文件
router.post('/cleanup', authenticateJWT, checkPermission('log_manage'), (req, res) => {
    try {
        const days = parseInt(req.body.days) || 30;
        
        const deletedCount = logViewer.cleanupOldLogs(days);
        
        logger.info('清理过期日志文件成功', { days, deletedCount, user: req.user });
        res.json({
            success: true,
            message: `成功清理 ${deletedCount} 个过期日志文件`,
            deletedCount
        });
    } catch (error) {
        logger.error('清理过期日志文件失败', { error: error.message, stack: error.stack, days: req.body.days });
        res.status(500).json({
            success: false,
            message: '清理过期日志文件失败: ' + error.message
        });
    }
});

module.exports = router;
