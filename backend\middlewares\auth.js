/**
 * 认证中间件
 * 处理用户认证和授权
 */

const jwt = require('jsonwebtoken');
const config = require('../config');
const { resolveCompletePermissions } = require('../config/permissionInheritance');

/**
 * JWT认证中间件
 * 验证请求中的JWT令牌
 */
function authenticateJWT(req, res, next) {
    const authHeader = req.headers.authorization;

    if (authHeader) {
        const token = authHeader.split(' ')[1];

        jwt.verify(token, config.jwt.secret, (err, user) => {
            if (err) {
                return res.status(403).json({
                    success: false,
                    message: '令牌无效或已过期'
                });
            }

            // 检查用户是否被禁用
            if (user.active === false) {
                return res.status(403).json({
                    success: false,
                    message: '账户已被禁用，请联系管理员'
                });
            }

            // 只有admin角色拥有所有权限
            const isSystemAdmin = user.role === 'admin';

            if (isSystemAdmin) {
                // 系统管理员的完整权限列表 - 按功能模块组织
                const adminPermissions = [
                    // 申请管理模块 (7个权限)
                    'application_view',
                    'application_create',
                    'application_edit',
                    'application_delete',
                    'application_approve',
                    'application_reject',
                    'application_download',

                    // 生产排程模块 (8个权限)
                    'schedule_view',
                    'schedule_create',
                    'schedule_edit',
                    'schedule_delete',
                    'schedule_execute',
                    'schedule_report',
                    'scheduling_algorithm',
                    'resource_manage',

                    // 设备管理模块 (8个权限)
                    'equipment_view',
                    'equipment_create',
                    'equipment_edit',
                    'equipment_delete',
                    'equipment_maintenance',
                    'equipment_health',
                    'equipment_capacity',
                    'equipment_export',

                    // 维护管理模块 (7个权限)
                    'maintenance_view',
                    'maintenance_create',
                    'maintenance_edit',
                    'maintenance_delete',
                    'maintenance_export',
                    'maintenance_import',
                    'maintenance_statistics',

                    // 质量管理模块 (6个权限)
                    'quality_view',
                    'quality_upload',
                    'quality_edit',
                    'quality_delete',
                    'quality_download',
                    'quality_manage',

                    // 文件管理模块 (7个权限)
                    'file_view',
                    'file_upload',
                    'file_download',
                    'file_edit',
                    'file_delete',
                    'file_confirm',
                    'file_manage',

                    // 产品管理模块 (5个权限)
                    'product_view',
                    'product_create',
                    'product_edit',
                    'product_delete',
                    'product_manage',

                    // 用户管理模块 (9个权限)
                    'user_view',
                    'user_create',
                    'user_edit',
                    'user_delete',
                    'user_export',
                    'user_import',
                    'user_signature',
                    'user_password_reset',
                    'user_permission_manage',

                    // 部门管理模块 (5个权限)
                    'department_view',
                    'department_create',
                    'department_edit',
                    'department_delete',
                    'department_manage',

                    // 权限管理模块 (4个权限)
                    'permission_view',
                    'permission_assign',
                    'permission_template',
                    'role_manage',

                    // 系统管理模块 (6个权限)
                    'system_config',
                    'system_monitor',
                    'system_backup',
                    'log_view',
                    'log_download',
                    'log_manage',

                    // 算法调优模块 (4个权限)
                    'tuning_view',
                    'tuning_edit',
                    'tuning_execute',
                    'algorithm_manage',

                    // 报表统计模块 (4个权限)
                    'report_view',
                    'report_create',
                    'report_export',
                    'statistics_view',

                    // 通用权限 (2个权限)
                    'user_settings',
                    'dashboard_view'
                ];

                // 确保admin角色有所有权限
                user.permissions = [...new Set([...(user.permissions || []), ...adminPermissions])];
            }

            req.user = user;
            next();
        });
    } else {
        res.status(401).json({
            success: false,
            message: '未提供认证令牌'
        });
    }
}

/**
 * 角色授权中间件
 * 检查用户是否具有指定角色
 * @param {string|Array} roles - 允许的角色
 */
function authorizeRoles(roles) {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: '未认证用户'
            });
        }

        const allowedRoles = Array.isArray(roles) ? roles : [roles];

        if (allowedRoles.includes(req.user.role)) {
            next();
        } else {
            res.status(403).json({
                success: false,
                message: '没有权限执行此操作'
            });
        }
    };
}

/**
 * 权限检查中间件
 * 检查用户是否具有指定权限
 * @param {string|Array} permissions - 需要的权限，可以是单个权限或权限数组
 * @param {string} logic - 权限检查逻辑，'AND' 或 'OR'，默认为 'OR'
 */
function checkPermission(permissions, logic = 'OR') {
    return (req, res, next) => {
        const user = req.user;

        if (!user) {
            return res.status(401).json({
                success: false,
                message: '未认证用户'
            });
        }

        // 只有admin角色拥有所有权限
        const isSystemAdmin = user.role === 'admin';

        if (isSystemAdmin) {
            return next();
        }

        // 标准化权限为数组
        const requiredPermissions = Array.isArray(permissions) ? permissions : [permissions];
        const userBasePermissions = user.permissions || [];

        // 解析完整权限（包括继承和依赖）
        const userCompletePermissions = resolveCompletePermissions(user.role, userBasePermissions);

        let hasPermission = false;

        if (logic === 'AND') {
            // AND逻辑：用户必须拥有所有指定权限
            hasPermission = requiredPermissions.every(perm => userCompletePermissions.includes(perm));
        } else {
            // OR逻辑：用户只需拥有任一指定权限
            hasPermission = requiredPermissions.some(perm => userCompletePermissions.includes(perm));
        }

        if (hasPermission) {
            return next();
        }

        return res.status(403).json({
            success: false,
            message: '权限不足',
            required: requiredPermissions,
            logic: logic
        });
    };
}

module.exports = {
    authenticateJWT,
    authorizeRoles,
    checkPermission
};
