/**
 * 权限继承机制配置
 * 定义权限之间的依赖关系和继承规则
 */

// 权限依赖关系 - 拥有某个权限时自动获得的其他权限
const PERMISSION_DEPENDENCIES = {
    // 管理权限自动包含查看权限
    'user_create': ['user_view'],
    'user_edit': ['user_view'],
    'user_delete': ['user_view'],
    'user_permission_manage': ['user_view', 'permission_view'],
    
    'department_create': ['department_view'],
    'department_edit': ['department_view'],
    'department_delete': ['department_view'],
    'department_manage': ['department_view'],
    
    'application_create': ['application_view'],
    'application_edit': ['application_view'],
    'application_delete': ['application_view'],
    'application_approve': ['application_view'],
    'application_reject': ['application_view'],
    
    'schedule_create': ['schedule_view'],
    'schedule_edit': ['schedule_view'],
    'schedule_delete': ['schedule_view'],
    'schedule_execute': ['schedule_view'],
    
    'equipment_create': ['equipment_view'],
    'equipment_edit': ['equipment_view'],
    'equipment_delete': ['equipment_view'],
    'equipment_maintenance': ['equipment_view'],
    'equipment_capacity': ['equipment_view'],
    
    'maintenance_create': ['maintenance_view'],
    'maintenance_edit': ['maintenance_view'],
    'maintenance_delete': ['maintenance_view'],
    'maintenance_export': ['maintenance_view'],
    'maintenance_statistics': ['maintenance_view'],
    
    'quality_upload': ['quality_view'],
    'quality_edit': ['quality_view'],
    'quality_delete': ['quality_view'],
    'quality_manage': ['quality_view'],
    
    'file_upload': ['file_view'],
    'file_edit': ['file_view'],
    'file_delete': ['file_view'],
    'file_manage': ['file_view'],
    
    'product_create': ['product_view'],
    'product_edit': ['product_view'],
    'product_delete': ['product_view'],
    
    // 高级权限包含基础权限
    'permission_assign': ['permission_view'],
    'permission_template': ['permission_view'],
    'role_manage': ['permission_view'],
    
    'system_config': ['system_monitor'],
    'system_backup': ['system_monitor'],
    'log_manage': ['log_view'],
    
    'tuning_edit': ['tuning_view'],
    'tuning_execute': ['tuning_view'],
    'algorithm_manage': ['tuning_view'],
    
    'report_create': ['report_view'],
    'report_export': ['report_view']
};

// 权限组合规则 - 某些权限组合会自动获得额外权限
const PERMISSION_COMBINATIONS = [
    // 拥有用户管理和部门管理权限时，自动获得权限管理权限
    {
        required: ['user_edit', 'department_manage'],
        grants: ['permission_view']
    },

    // 拥有设备和维护管理权限时，自动获得设备健康监控权限
    {
        required: ['equipment_edit', 'maintenance_create'],
        grants: ['equipment_health']
    },

    // 拥有申请审批权限时，自动获得文件下载权限
    {
        required: ['application_approve'],
        grants: ['file_download', 'application_download']
    },

    // 拥有质量管理权限时，自动获得文件相关权限
    {
        required: ['quality_manage'],
        grants: ['file_view', 'file_download']
    },

    // 拥有系统配置权限时，自动获得日志管理权限
    {
        required: ['system_config'],
        grants: ['log_view', 'log_manage']
    },

    // 拥有算法调优权限时，自动获得排程算法权限
    {
        required: ['tuning_execute'],
        grants: ['scheduling_algorithm']
    }
];

// 角色权限继承 - 基于角色的权限继承
const ROLE_PERMISSION_INHERITANCE = {
    'admin': {
        inherits: [], // 管理员不继承其他角色
        autoPermissions: [
            // 申请管理模块 (7个权限)
            'application_view', 'application_create', 'application_edit', 'application_delete',
            'application_approve', 'application_reject', 'application_download',

            // 用户管理模块 (8个权限)
            'user_view', 'user_create', 'user_edit', 'user_delete',
            'user_permission_manage', 'user_signature', 'user_export', 'user_import',

            // 部门管理模块 (5个权限)
            'department_view', 'department_create', 'department_edit', 'department_delete', 'department_manage',

            // 设备管理模块 (8个权限)
            'equipment_view', 'equipment_create', 'equipment_edit', 'equipment_delete',
            'equipment_maintain', 'equipment_health', 'equipment_export', 'equipment_import',

            // 维护管理模块 (7个权限)
            'maintenance_view', 'maintenance_create', 'maintenance_edit', 'maintenance_delete',
            'maintenance_assign', 'maintenance_complete', 'maintenance_export',

            // 质量管理模块 (6个权限)
            'quality_view', 'quality_create', 'quality_edit', 'quality_delete',
            'quality_approve', 'quality_export',

            // 文件管理模块 (8个权限)
            'file_view', 'file_upload', 'file_download', 'file_delete',
            'file_manage', 'file_version', 'customer_manage', 'file_export',

            // 报告管理模块 (6个权限)
            'report_view', 'report_create', 'report_edit', 'report_delete',
            'report_export', 'report_schedule',

            // 统计分析模块 (5个权限)
            'statistics_view', 'statistics_export', 'analytics_view', 'analytics_export', 'dashboard_view',

            // 系统配置模块 (6个权限)
            'system_config', 'system_monitor', 'system_backup', 'system_restore',
            'system_update', 'system_maintenance',

            // 权限管理模块 (6个权限)
            'permission_view', 'permission_assign', 'permission_template', 'permission_audit',
            'role_manage', 'role_assign',

            // 日志管理模块 (4个权限)
            'log_view', 'log_export', 'log_delete', 'log_manage',

            // 通知管理模块 (5个权限)
            'notification_view', 'notification_send', 'notification_manage', 'notification_template', 'email_send',

            // 通用权限模块 (2个权限)
            'user_settings', 'dashboard_view'
        ]
    },
    'manager': {
        inherits: ['user'], // 管理员继承普通用户权限
        autoPermissions: [
            'application_approve', 'application_reject',
            'user_view', 'department_view',
            'report_view', 'statistics_view'
        ]
    },
    'user': {
        inherits: [], // 普通用户不继承其他角色
        autoPermissions: [
            'user_settings', 'dashboard_view',
            'application_view', 'application_create'
        ]
    }
};

/**
 * 解析权限依赖关系
 * @param {Array} permissions - 原始权限列表
 * @returns {Array} 包含依赖权限的完整权限列表
 */
function resolvePermissionDependencies(permissions) {
    const resolvedPermissions = new Set(permissions);
    
    // 添加直接依赖的权限
    for (const permission of permissions) {
        if (PERMISSION_DEPENDENCIES[permission]) {
            PERMISSION_DEPENDENCIES[permission].forEach(dep => {
                resolvedPermissions.add(dep);
            });
        }
    }
    
    // 检查权限组合规则
    for (const combination of PERMISSION_COMBINATIONS) {
        const requiredPermissions = combination.required;
        const additionalPermissions = combination.grants;

        // 检查是否拥有组合中的所有权限
        if (requiredPermissions.every(perm => resolvedPermissions.has(perm))) {
            additionalPermissions.forEach(perm => {
                resolvedPermissions.add(perm);
            });
        }
    }
    
    return Array.from(resolvedPermissions);
}

/**
 * 基于角色解析权限继承
 * @param {string} role - 用户角色
 * @param {Array} permissions - 用户权限
 * @returns {Array} 包含继承权限的完整权限列表
 */
function resolveRolePermissionInheritance(role, permissions) {
    const resolvedPermissions = new Set(permissions);
    
    if (ROLE_PERMISSION_INHERITANCE[role]) {
        const roleConfig = ROLE_PERMISSION_INHERITANCE[role];
        
        // 添加角色自动权限
        roleConfig.autoPermissions.forEach(perm => {
            resolvedPermissions.add(perm);
        });
        
        // 递归处理继承的角色
        for (const inheritedRole of roleConfig.inherits) {
            const inheritedPermissions = resolveRolePermissionInheritance(
                inheritedRole, 
                Array.from(resolvedPermissions)
            );
            inheritedPermissions.forEach(perm => {
                resolvedPermissions.add(perm);
            });
        }
    }
    
    return Array.from(resolvedPermissions);
}

/**
 * 完整的权限解析 - 同时处理依赖关系和角色继承
 * @param {string} role - 用户角色
 * @param {Array} permissions - 用户权限
 * @returns {Array} 最终的完整权限列表
 */
function resolveCompletePermissions(role, permissions) {
    // 首先解析角色继承
    let resolvedPermissions = resolveRolePermissionInheritance(role, permissions);
    
    // 然后解析权限依赖关系
    resolvedPermissions = resolvePermissionDependencies(resolvedPermissions);
    
    return resolvedPermissions;
}

/**
 * 验证权限配置的一致性
 * @returns {Object} 验证结果
 */
function validatePermissionConfiguration() {
    const issues = [];
    
    // 检查循环依赖
    for (const [permission, dependencies] of Object.entries(PERMISSION_DEPENDENCIES)) {
        if (dependencies.includes(permission)) {
            issues.push(`权限 ${permission} 存在自我依赖`);
        }
        
        // 检查深层循环依赖（简单检查）
        for (const dep of dependencies) {
            if (PERMISSION_DEPENDENCIES[dep] && PERMISSION_DEPENDENCIES[dep].includes(permission)) {
                issues.push(`权限 ${permission} 和 ${dep} 存在循环依赖`);
            }
        }
    }
    
    // 检查角色继承循环
    for (const [role, config] of Object.entries(ROLE_PERMISSION_INHERITANCE)) {
        if (config.inherits.includes(role)) {
            issues.push(`角色 ${role} 存在自我继承`);
        }
    }
    
    return {
        isValid: issues.length === 0,
        issues
    };
}

module.exports = {
    PERMISSION_DEPENDENCIES,
    PERMISSION_COMBINATIONS,
    ROLE_PERMISSION_INHERITANCE,
    resolvePermissionDependencies,
    resolveRolePermissionInheritance,
    resolveCompletePermissions,
    validatePermissionConfiguration
};
