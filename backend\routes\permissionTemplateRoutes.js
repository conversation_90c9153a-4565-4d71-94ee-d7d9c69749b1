/**
 * 权限模板路由
 * 处理权限模板相关的路由
 */

const express = require('express');
const router = express.Router();
const permissionTemplateController = require('../controllers/permissionTemplateController');
const { authenticateJWT, checkPermission } = require('../middlewares/auth');

// 获取专业权限模板 (必须在 /:id 之前定义)
router.get('/professional', authenticateJWT, checkPermission('permission_template'), permissionTemplateController.getProfessionalTemplates);

// 应用专业权限模板
router.post('/professional/apply', authenticateJWT, checkPermission('permission_template'), permissionTemplateController.applyProfessionalTemplate);

// 获取所有权限模板
router.get('/', authenticateJWT, permissionTemplateController.getAllTemplates);

// 根据ID获取权限模板
router.get('/:id', authenticateJWT, permissionTemplateController.getTemplateById);

// 创建权限模板
router.post('/', authenticateJWT, permissionTemplateController.createTemplate);

// 更新权限模板
router.put('/:id', authenticateJWT, permissionTemplateController.updateTemplate);

// 删除权限模板
router.delete('/:id', authenticateJWT, permissionTemplateController.deleteTemplate);

module.exports = router;
