/**
 * API测试脚本
 * 测试权限模板API是否正常工作
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

// 测试用户登录
async function testLogin() {
    try {
        console.log('🔐 测试用户登录...');
        const response = await axios.post(`${API_BASE}/auth/login`, {
            usercode: 'admin',
            password: 'admin123'
        });
        
        if (response.data.success) {
            console.log('✅ 登录成功');
            return response.data.token;
        } else {
            console.log('❌ 登录失败:', response.data.message);
            return null;
        }
    } catch (error) {
        console.log('❌ 登录异常:', error.message);
        return null;
    }
}

// 测试专业权限模板API
async function testProfessionalTemplates(token) {
    try {
        console.log('\n📋 测试专业权限模板API...');
        const response = await axios.get(`${API_BASE}/permission-templates/professional`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.data.success) {
            console.log('✅ 获取专业权限模板成功');
            console.log('模板数量:', response.data.templates.length);
            response.data.templates.forEach(template => {
                console.log(`- ${template.name}: ${template.permissions.length}个权限`);
            });
            return true;
        } else {
            console.log('❌ 获取专业权限模板失败:', response.data.message);
            return false;
        }
    } catch (error) {
        console.log('❌ 专业权限模板API异常:', error.response?.status, error.response?.data?.message || error.message);
        return false;
    }
}

// 测试用户权限API
async function testUserPermissions(token) {
    try {
        console.log('\n👤 测试用户权限API...');
        // 假设管理员用户ID为1
        const response = await axios.get(`${API_BASE}/users/1/permissions`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.data.success) {
            console.log('✅ 获取用户权限成功');
            console.log('权限数量:', response.data.permissions.length);
            return true;
        } else {
            console.log('❌ 获取用户权限失败:', response.data.message);
            return false;
        }
    } catch (error) {
        console.log('❌ 用户权限API异常:', error.response?.status, error.response?.data?.message || error.message);
        return false;
    }
}

// 主测试函数
async function runTests() {
    console.log('🧪 开始API测试...\n');
    
    // 1. 测试登录
    const token = await testLogin();
    if (!token) {
        console.log('\n❌ 无法获取认证token，测试终止');
        return;
    }
    
    // 2. 测试专业权限模板API
    const templatesOk = await testProfessionalTemplates(token);
    
    // 3. 测试用户权限API
    const permissionsOk = await testUserPermissions(token);
    
    // 总结
    console.log('\n📊 测试结果总结:');
    console.log('- 用户登录:', '✅');
    console.log('- 专业权限模板API:', templatesOk ? '✅' : '❌');
    console.log('- 用户权限API:', permissionsOk ? '✅' : '❌');
    
    if (templatesOk && permissionsOk) {
        console.log('\n🎉 所有API测试通过！');
    } else {
        console.log('\n⚠️ 部分API测试失败，请检查服务器日志');
    }
}

// 运行测试
runTests().catch(error => {
    console.error('测试运行异常:', error);
});
