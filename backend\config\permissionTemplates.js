/**
 * 专业权限模板配置
 * 为不同岗位提供预设的权限组合
 */

const PROFESSIONAL_PERMISSION_TEMPLATES = {
    // 设备操作员模板
    equipment_operator: {
        name: '设备操作员',
        description: '负责设备操作和基础维护的人员',
        permissions: [
            // 设备相关权限
            'equipment_view',
            'equipment_health',
            'equipment_capacity',
            
            // 维护相关权限
            'maintenance_view',
            'maintenance_create',
            
            // 生产排程相关权限
            'schedule_view',
            'schedule_execute',
            
            // 质量相关权限
            'quality_view',
            'quality_upload',
            
            // 通用权限
            'user_settings',
            'dashboard_view'
        ]
    },

    // 质量检验员模板
    quality_inspector: {
        name: '质量检验员',
        description: '负责产品质量检验和数据管理的人员',
        permissions: [
            // 质量管理权限
            'quality_view',
            'quality_upload',
            'quality_edit',
            'quality_download',
            'quality_manage',
            
            // 文件管理权限
            'file_view',
            'file_upload',
            'file_download',
            
            // 产品相关权限
            'product_view',
            
            // 报表权限
            'report_view',
            'statistics_view',
            
            // 通用权限
            'user_settings',
            'dashboard_view'
        ]
    },

    // 维护技术员模板
    maintenance_technician: {
        name: '维护技术员',
        description: '负责设备维护和故障处理的技术人员',
        permissions: [
            // 维护管理权限
            'maintenance_view',
            'maintenance_create',
            'maintenance_edit',
            'maintenance_delete',
            'maintenance_export',
            'maintenance_statistics',
            
            // 设备管理权限
            'equipment_view',
            'equipment_edit',
            'equipment_maintenance',
            'equipment_health',
            'equipment_capacity',
            
            // 文件管理权限
            'file_view',
            'file_upload',
            'file_download',
            
            // 通用权限
            'user_settings',
            'dashboard_view'
        ]
    },

    // 生产计划员模板
    production_planner: {
        name: '生产计划员',
        description: '负责生产排程和计划管理的人员',
        permissions: [
            // 生产排程权限
            'schedule_view',
            'schedule_create',
            'schedule_edit',
            'schedule_delete',
            'schedule_execute',
            'schedule_report',
            'resource_manage',
            
            // 产品管理权限
            'product_view',
            'product_create',
            'product_edit',
            
            // 设备查看权限
            'equipment_view',
            'equipment_health',
            'equipment_capacity',
            
            // 申请管理权限
            'application_view',
            'application_create',
            
            // 报表权限
            'report_view',
            'report_create',
            'statistics_view',
            
            // 通用权限
            'user_settings',
            'dashboard_view'
        ]
    },

    // 部门经理模板
    department_manager: {
        name: '部门经理',
        description: '负责部门管理和审批的管理人员',
        permissions: [
            // 部门管理权限
            'department_view',
            'department_edit',
            'department_manage',
            
            // 用户管理权限
            'user_view',
            'user_edit',
            
            // 申请审批权限
            'application_view',
            'application_approve',
            'application_reject',
            'application_download',
            
            // 生产排程权限
            'schedule_view',
            'schedule_create',
            'schedule_edit',
            'schedule_report',
            
            // 设备管理权限
            'equipment_view',
            'equipment_health',
            'equipment_capacity',
            
            // 维护管理权限
            'maintenance_view',
            'maintenance_statistics',
            
            // 质量管理权限
            'quality_view',
            'quality_manage',
            
            // 文件管理权限
            'file_view',
            'file_manage',
            
            // 报表权限
            'report_view',
            'report_create',
            'report_export',
            'statistics_view',
            
            // 通用权限
            'user_settings',
            'dashboard_view'
        ]
    },

    // 系统管理员模板
    system_administrator: {
        name: '系统管理员',
        description: '负责系统配置和维护的技术管理员',
        permissions: [
            // 系统管理权限
            'system_config',
            'system_monitor',
            'system_backup',
            'log_view',
            'log_download',
            'log_manage',
            
            // 用户管理权限
            'user_view',
            'user_create',
            'user_edit',
            'user_delete',
            'user_export',
            'user_import',
            'user_signature',
            'user_password_reset',
            'user_permission_manage',
            
            // 部门管理权限
            'department_view',
            'department_create',
            'department_edit',
            'department_delete',
            'department_manage',
            
            // 权限管理权限
            'permission_view',
            'permission_assign',
            'permission_template',
            'role_manage',
            
            // 算法调优权限
            'tuning_view',
            'tuning_edit',
            'tuning_execute',
            'algorithm_manage',
            
            // 通用权限
            'user_settings',
            'dashboard_view'
        ]
    },

    // 文件管理员模板
    file_administrator: {
        name: '文件管理员',
        description: '负责文件管理和归档的专员',
        permissions: [
            // 文件管理权限
            'file_view',
            'file_upload',
            'file_download',
            'file_edit',
            'file_delete',
            'file_confirm',
            'file_manage',
            
            // 申请相关权限
            'application_view',
            'application_download',
            
            // 质量文件权限
            'quality_view',
            'quality_download',
            
            // 维护文件权限
            'maintenance_view',
            'maintenance_export',
            
            // 通用权限
            'user_settings',
            'dashboard_view'
        ]
    },

    // 普通员工模板
    general_employee: {
        name: '普通员工',
        description: '基础权限的普通员工',
        permissions: [
            // 申请权限
            'application_view',
            'application_create',
            
            // 基础查看权限
            'schedule_view',
            'equipment_view',
            'product_view',
            
            // 文件查看权限
            'file_view',
            'file_download',
            
            // 通用权限
            'user_settings',
            'dashboard_view'
        ]
    }
};

module.exports = {
    PROFESSIONAL_PERMISSION_TEMPLATES
};
