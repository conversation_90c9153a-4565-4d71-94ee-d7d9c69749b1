---
type: "always_apply"
---

# 开发规范文档 (Development Rules)

## 📋 概述

本文档规定了项目开发过程中必须遵循的规范，确保代码质量和文档的及时更新。

## 🔧 代码完成后的必要步骤

### 1. 文档更新要求

每次完成代码开发后，**必须**按以下顺序更新相关文档：

#### 📖 README.md 更新
- **何时更新**: 每次添加新功能、修改现有功能、或修复重要bug后
- **更新内容**:
  - 项目描述和功能概述
  - 安装和运行说明
  - API文档或使用示例
  - 依赖项变更
  - 配置说明
  - 已知问题和限制

#### 🔧 func.md 更新
- **何时更新**: 每次代码提交后
- **更新内容**:
  - 新增系统功能详细说明
  - 功能模块的技术实现
  - 功能特性和使用方法
  - 功能间的依赖关系
  - 功能配置和参数说明
  - 功能的性能指标和限制

### 2. 更新流程

```mermaid
graph TD
    A[完成代码开发] --> B[运行测试]
    B --> C[测试通过?]
    C -->|否| D[修复问题]
    D --> B
    C -->|是| E[更新 README.md]
    E --> F[更新 func.md]
    F --> G[提交代码和文档]
    G --> H[完成]
```

### 3. 文档质量标准

#### README.md 标准
- ✅ 清晰的项目标题和描述
- ✅ 完整的安装步骤
- ✅ 详细的使用说明
- ✅ API文档（如适用）
- ✅ 贡献指南
- ✅ 许可证信息

#### func.md 标准
- ✅ 系统功能模块说明
- ✅ 功能实现技术细节
- ✅ 功能使用方法和示例
- ✅ 功能配置参数说明
- ✅ 功能性能指标
- ✅ 功能限制和注意事项

## 🚫 违规处理

### 轻微违规
- 忘记更新文档 → 立即补充更新
- 文档内容不完整 → 完善相关内容

### 严重违规
- 连续多次不更新文档 → 代码回滚，重新提交
- 文档与代码严重不符 → 全面审查和修正

## 📝 文档模板

### README.md 基础模板
```markdown
# 项目名称

## 描述
简要描述项目的目的和功能

## 安装
\`\`\`bash
npm install
\`\`\`

## 使用方法
\`\`\`bash
npm start
\`\`\`

## API文档
详细的API使用说明

## 贡献
如何为项目做贡献

## 许可证
项目许可证信息
```

### func.md 基础模板
```markdown
# 系统功能文档

## 最新更新 (日期)
### 🔧 新增功能
- 功能名称1：功能描述和用途
- 功能名称2：功能描述和用途

### 💡 技术实现
- 实现方案1：技术栈和架构说明
- 实现方案2：技术栈和架构说明

### � 功能配置
- 配置参数说明
- 使用方法和示例

### ⚠️ 注意事项
- 功能限制
- 使用注意事项
```

## 🔍 检查清单

在每次代码提交前，请确认：

- [ ] 代码功能正常运行
- [ ] 所有测试通过
- [ ] README.md 已更新
- [ ] func.md 已更新
- [ ] 文档内容准确无误
- [ ] 代码和文档版本一致

## 📞 支持

如有疑问或建议，请联系项目维护者。

---

**记住**: 好的文档是好代码的一部分！📚✨
