/**
 * 申请路由
 * 处理申请相关的路由
 */

const express = require('express');
const router = express.Router();
const applicationController = require('../controllers/applicationController');
const { authenticateJWT, checkPermission } = require('../middlewares/auth');
const upload = require('../middlewares/upload');

// 获取所有申请
router.get('/',
    authenticateJWT,
    checkPermission('application_view'),
    applicationController.getAllApplications
);

// 获取待审核申请
router.get('/pending',
    authenticateJWT,
    checkPermission(['application_approve', 'application_view'], 'OR'),
    applicationController.getPendingApplications
);

// 获取已审核申请
router.get('/approved',
    authenticateJWT,
    checkPermission('application_view'),
    applicationController.getApprovedApplications
);

// 获取申请记录
router.get('/records',
    authenticateJWT,
    checkPermission('application_view'),
    applicationController.getApplicationRecords
);

// 审批通过申请
router.post('/:id/approve',
    authenticateJWT,
    checkPermission('application_approve'),
    applicationController.approveApplication
);

// 审批拒绝申请
router.post('/:id/reject',
    authenticateJWT,
    checkPermission('application_reject'),
    applicationController.rejectApplication
);

// 获取单个申请详情
router.get('/:id',
    authenticateJWT,
    checkPermission('application_view'),
    applicationController.getApplicationById
);

// 创建新申请
router.post('/',
    authenticateJWT,
    checkPermission('application_create'),
    upload.array('attachments', 10),
    applicationController.createApplication
);

// 修改申请
router.put('/:id',
    authenticateJWT,
    checkPermission('application_edit'),
    upload.array('attachments', 10),
    applicationController.updateApplication
);

// 删除申请
router.delete('/:id',
    authenticateJWT,
    checkPermission('application_delete'),
    applicationController.deleteApplication
);

// 下载附件
router.get('/attachments/:id',
    authenticateJWT,
    checkPermission('application_download'),
    applicationController.downloadAttachment
);

module.exports = router;
