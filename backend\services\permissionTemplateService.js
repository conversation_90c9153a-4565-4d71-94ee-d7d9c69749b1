/**
 * 权限模板服务
 * 处理权限模板的数据操作
 * 使用SQLite数据库
 */

const fs = require('fs');
const path = require('path');
const config = require('../config');
const logger = require('../utils/logger');
const permissionTemplateRepository = require('../database/permissionTemplateRepository');

/**
 * 读取权限模板文件（保留用于兼容性）
 * @returns {Array} 权限模板列表
 */
function readTemplatesFile() {
    try {
        return permissionTemplateRepository.findAll();
    } catch (error) {
        logger.error('读取权限模板失败:', error);
        return [];
    }
}

/**
 * 写入权限模板文件（保留用于兼容性）
 * @param {Array} templates - 权限模板列表
 * @returns {boolean} 是否成功
 */
function writeTemplatesFile(templates) {
    logger.warn('writeTemplatesFile方法已废弃，请使用具体的创建/更新方法');
    return true; // 保持兼容性
}

/**
 * 获取预设权限模板（保留用于兼容性）
 * @returns {Array} 预设权限模板列表
 */
function getBuiltInTemplates() {
    try {
        const allTemplates = permissionTemplateRepository.findAll();
        return allTemplates.filter(template => template.isBuiltIn);
    } catch (error) {
        logger.error('获取预设权限模板失败:', error);
        return [];
    }
}

/**
 * 获取所有权限模板（包括预设模板和自定义模板）
 * @returns {Array} 权限模板列表
 */
function getAllTemplates() {
    try {
        return permissionTemplateRepository.findAll();
    } catch (error) {
        logger.error('获取所有权限模板失败:', error);
        return [];
    }
}

/**
 * 根据ID获取权限模板
 * @param {string} id - 模板ID
 * @returns {Object|null} 权限模板
 */
function getTemplateById(id) {
    try {
        return permissionTemplateRepository.findById(id);
    } catch (error) {
        logger.error(`根据ID获取权限模板失败 (${id}):`, error);
        return null;
    }
}

/**
 * 创建权限模板
 * @param {Object} templateData - 模板数据
 * @returns {Object|null} 创建的模板
 */
function createTemplate(templateData) {
    try {
        const newTemplate = permissionTemplateRepository.create(templateData);
        logger.info(`权限模板创建成功: ${newTemplate.name} (ID: ${newTemplate.id})`);
        return newTemplate;
    } catch (error) {
        logger.error('创建权限模板失败:', error);
        return null;
    }
}

/**
 * 更新权限模板
 * @param {string} id - 模板ID
 * @param {Object} updateData - 更新数据
 * @returns {Object|null} 更新后的模板
 */
function updateTemplate(id, updateData) {
    try {
        const existingTemplate = permissionTemplateRepository.findById(id);
        if (!existingTemplate) {
            return null;
        }

        // 如果是内置模板，创建一个新的自定义模板
        if (existingTemplate.isBuiltIn) {
            const newTemplate = {
                name: updateData.name !== undefined ? updateData.name : existingTemplate.name,
                description: updateData.description !== undefined ? updateData.description : existingTemplate.description,
                permissions: updateData.permissions !== undefined ? updateData.permissions : existingTemplate.permissions,
                isBuiltIn: false
            };

            const createdTemplate = permissionTemplateRepository.create(newTemplate);
            logger.info(`预设模板转换为自定义模板: ${createdTemplate.name} (新ID: ${createdTemplate.id})`);
            return createdTemplate;
        } else {
            // 更新自定义模板
            const updatedTemplate = permissionTemplateRepository.update(id, updateData);
            if (updatedTemplate) {
                logger.info(`权限模板更新成功: ${updatedTemplate.name} (ID: ${id})`);
            }
            return updatedTemplate;
        }
    } catch (error) {
        logger.error('更新权限模板失败:', error);
        return null;
    }
}

/**
 * 删除权限模板
 * @param {string} id - 模板ID
 * @returns {boolean} 是否成功
 */
function deleteTemplate(id) {
    try {
        const template = permissionTemplateRepository.findById(id);
        if (!template) {
            return false;
        }

        if (template.isBuiltIn) {
            // 预设模板不能删除
            logger.warn(`尝试删除预设模板: ${id}`);
            return false;
        }

        const success = permissionTemplateRepository.delete(id);
        if (success) {
            logger.info(`权限模板删除成功: ${template.name} (ID: ${id})`);
        }
        return success;
    } catch (error) {
        logger.error('删除权限模板失败:', error);
        return false;
    }
}

/**
 * 应用权限到用户（支持专业模板）
 * @param {Array} userIds - 用户ID数组
 * @param {Array} permissions - 权限数组
 * @returns {Object} 应用结果
 */
async function applyPermissionsToUsers(userIds, permissions) {
    const db = require('../config/database');

    try {
        const appliedUsers = [];
        const failedUsers = [];

        for (const userId of userIds) {
            try {
                // 更新用户权限
                const stmt = db.prepare(`
                    UPDATE users
                    SET permissions = ?
                    WHERE id = ?
                `);

                const result = stmt.run(JSON.stringify(permissions), userId);

                if (result.changes > 0) {
                    appliedUsers.push(userId);
                    logger.info('权限应用成功', { userId, permissions });
                } else {
                    failedUsers.push({ userId, reason: '用户不存在' });
                }
            } catch (error) {
                logger.error('应用权限失败', { userId, error: error.message });
                failedUsers.push({ userId, reason: error.message });
            }
        }

        return {
            success: true,
            appliedUsers,
            failedUsers,
            message: `成功应用权限到 ${appliedUsers.length} 个用户`
        };
    } catch (error) {
        logger.error('批量应用权限失败:', error);
        return {
            success: false,
            message: '批量应用权限失败: ' + error.message
        };
    }
}

/**
 * 检查模板名称是否已存在
 * @param {string} name - 模板名称
 * @param {string} excludeId - 排除的模板ID（用于更新时检查）
 * @returns {boolean} 是否存在
 */
function isTemplateNameExists(name, excludeId = null) {
    try {
        return permissionTemplateRepository.isNameExists(name, excludeId || '');
    } catch (error) {
        logger.error('检查模板名称是否存在失败:', error);
        return false;
    }
}

module.exports = {
    getAllTemplates,
    getTemplateById,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    isTemplateNameExists,
    applyPermissionsToUsers
};
