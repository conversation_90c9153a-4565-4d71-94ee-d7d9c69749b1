/**
 * 权限更新测试脚本
 * 测试权限保存和加载的一致性
 */

const axios = require('axios');

const API_BASE = 'http://localhost:5050/api';

// 测试用户登录
async function testLogin() {
    try {
        console.log('🔐 测试用户登录...');
        const response = await axios.post(`${API_BASE}/auth/login`, {
            usercode: 'admin',
            password: 'admin123'
        });
        
        if (response.data.success) {
            console.log('✅ 登录成功');
            return response.data.token;
        } else {
            console.log('❌ 登录失败:', response.data.message);
            return null;
        }
    } catch (error) {
        console.log('❌ 登录异常:', error.message);
        return null;
    }
}

// 获取用户权限
async function getUserPermissions(token, userId) {
    try {
        const response = await axios.get(`${API_BASE}/users/${userId}/permissions`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.data.success) {
            return response.data.permissions;
        } else {
            console.log('❌ 获取用户权限失败:', response.data.message);
            return null;
        }
    } catch (error) {
        console.log('❌ 获取用户权限异常:', error.response?.status, error.response?.data?.message || error.message);
        return null;
    }
}

// 更新用户权限
async function updateUserPermissions(token, userId, permissions) {
    try {
        const response = await axios.put(`${API_BASE}/users/${userId}/permissions`, {
            permissions: permissions
        }, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.data.success) {
            return true;
        } else {
            console.log('❌ 更新用户权限失败:', response.data.message);
            return false;
        }
    } catch (error) {
        console.log('❌ 更新用户权限异常:', error.response?.status, error.response?.data?.message || error.message);
        return false;
    }
}

// 测试权限更新一致性
async function testPermissionConsistency(token, userId) {
    console.log(`\n📋 测试用户 ${userId} 的权限更新一致性...`);
    
    // 1. 获取当前权限
    console.log('1. 获取当前权限...');
    const originalPermissions = await getUserPermissions(token, userId);
    if (!originalPermissions) {
        console.log('❌ 无法获取原始权限');
        return false;
    }
    console.log(`   当前权限数量: ${originalPermissions.length}`);
    
    // 2. 添加一个测试权限
    console.log('2. 添加测试权限...');
    const testPermission = 'user_settings';
    const newPermissions = [...originalPermissions];
    if (!newPermissions.includes(testPermission)) {
        newPermissions.push(testPermission);
    }
    
    const updateSuccess = await updateUserPermissions(token, userId, newPermissions);
    if (!updateSuccess) {
        console.log('❌ 权限更新失败');
        return false;
    }
    console.log('   权限更新成功');
    
    // 3. 重新获取权限验证
    console.log('3. 验证权限更新...');
    const updatedPermissions = await getUserPermissions(token, userId);
    if (!updatedPermissions) {
        console.log('❌ 无法获取更新后的权限');
        return false;
    }
    
    console.log(`   更新后权限数量: ${updatedPermissions.length}`);
    
    // 4. 检查权限是否包含测试权限
    const hasTestPermission = updatedPermissions.includes(testPermission);
    console.log(`   包含测试权限 ${testPermission}: ${hasTestPermission ? '✅' : '❌'}`);
    
    // 5. 恢复原始权限
    console.log('4. 恢复原始权限...');
    const restoreSuccess = await updateUserPermissions(token, userId, originalPermissions);
    if (!restoreSuccess) {
        console.log('❌ 权限恢复失败');
        return false;
    }
    console.log('   权限恢复成功');
    
    return hasTestPermission;
}

// 主测试函数
async function runTests() {
    console.log('🧪 开始权限更新一致性测试...\n');
    
    // 1. 测试登录
    const token = await testLogin();
    if (!token) {
        console.log('\n❌ 无法获取认证token，测试终止');
        return;
    }
    
    // 2. 测试管理员用户权限一致性
    const adminTestResult = await testPermissionConsistency(token, 1);
    
    // 3. 如果有其他用户，也测试一下
    let userTestResult = true;
    try {
        userTestResult = await testPermissionConsistency(token, 2);
    } catch (error) {
        console.log('   普通用户测试跳过（用户不存在）');
    }
    
    // 总结
    console.log('\n📊 测试结果总结:');
    console.log('- 用户登录:', '✅');
    console.log('- 管理员权限一致性:', adminTestResult ? '✅' : '❌');
    console.log('- 普通用户权限一致性:', userTestResult ? '✅' : '❌');
    
    if (adminTestResult && userTestResult) {
        console.log('\n🎉 权限更新一致性测试通过！');
    } else {
        console.log('\n⚠️ 权限更新一致性测试失败，请检查代码');
    }
}

// 运行测试
runTests().catch(error => {
    console.error('测试运行异常:', error);
});
