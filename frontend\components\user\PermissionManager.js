/**
 * 用户权限管理组件
 * 用于管理用户可访问的页面和功能
 */

import { updateUserPermissions, getUserPermissions, getUsers, getPermissionTemplates } from '../../scripts/api/user.js';

export default {
    props: {
        user: Object,
        currentUser: Object
    },
    emits: ['updated', 'batch-updated', 'back'],
    setup(props, { emit }) {
        const { ref, reactive, onMounted, computed, watch, nextTick } = Vue;

        // 状态变量
        const isLoading = ref(false);
        const isSaving = ref(false);
        const userPermissions = ref([]);
        const availablePermissions = ref([]);
        const permissionGroups = ref([]);

        // 模板和批量应用相关状态
        const showTemplateDropdown = ref(false);
        const showBatchDropdown = ref(false);
        const permissionTemplates = ref([]);
        const showBatchApplyModal = ref(false);
        const isLoadingBatchUsers = ref(false);
        const isBatchSaving = ref(false);
        const batchUsers = ref([]);
        const selectedUsers = ref([]);
        const userSearchTerm = ref('');

        // 初始化
        onMounted(() => {
            initializePermissions();
            loadUserPermissions();
            loadPermissionTemplates();

            // 点击外部关闭下拉菜单
            document.addEventListener('click', (event) => {
                const templateContainer = templateDropdownContainer.value;
                const batchContainer = batchDropdownContainer.value;

                if (templateContainer && !templateContainer.contains(event.target)) {
                    showTemplateDropdown.value = false;
                }

                if (batchContainer && !batchContainer.contains(event.target)) {
                    showBatchDropdown.value = false;
                }
            });
        });

        // 下拉菜单引用
        const templateDropdownContainer = ref(null);
        const batchDropdownContainer = ref(null);

        // 加载权限模板
        async function loadPermissionTemplates() {
            try {
                const response = await getPermissionTemplates();
                if (response.success) {
                    // 只显示自定义模板，过滤掉预设模板
                    permissionTemplates.value = response.templates.filter(template => !template.isBuiltIn);
                }
            } catch (error) {
                console.error('加载权限模板失败:', error);
            }
        }

        // 专业权限模板
        const professionalTemplates = ref([]);

        // 加载专业权限模板
        async function loadProfessionalTemplates() {
            try {
                const token = sessionStorage.getItem('authToken') || localStorage.getItem('token');
                const response = await fetch('/api/permission-templates/professional', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        professionalTemplates.value = result.templates;
                    }
                } else {
                    console.error('加载专业权限模板失败:', response.status, response.statusText);
                }
            } catch (error) {
                console.error('加载专业权限模板失败:', error);
            }
        }

        // 应用专业权限模板
        async function applyProfessionalTemplate(templateId) {
            if (!props.user || !props.user.id) return;

            try {
                const token = sessionStorage.getItem('authToken') || localStorage.getItem('token');
                const response = await fetch('/api/permission-templates/professional/apply', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        templateId,
                        userIds: [props.user.id]
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        console.log('✅ 专业权限模板应用成功');

                        // 添加短暂延迟确保数据库写入完成
                        await new Promise(resolve => setTimeout(resolve, 100));

                        // 重新加载用户权限
                        console.log('🔄 重新加载用户权限...');
                        await loadUserPermissions();

                        // 显示成功消息
                        alert('专业权限模板应用成功！');
                    } else {
                        console.error('❌ 应用专业权限模板失败:', result.message);
                        alert('应用专业权限模板失败: ' + result.message);
                    }
                } else {
                    console.error('❌ 专业权限模板应用请求失败:', response.status);
                    alert('应用专业权限模板失败');
                }
            } catch (error) {
                console.error('应用专业权限模板失败:', error);
                alert('应用专业权限模板失败');
            }
        }

        // 初始化权限列表
        function initializePermissions() {
            // 定义系统中的所有页面和功能
            permissionGroups.value = [
                {
                    id: 'applications',
                    name: '申请管理',
                    permissions: [
                        { id: 'application_view', name: '查看申请', description: '允许用户查看申请列表和详情' },
                        { id: 'application_create', name: '创建申请', description: '允许用户创建新的申请' },
                        { id: 'application_edit', name: '编辑申请', description: '允许用户编辑申请' },
                        { id: 'application_delete', name: '删除申请', description: '允许用户删除申请' },
                        { id: 'application_approve', name: '审批申请', description: '允许用户审批申请' },
                        { id: 'application_reject', name: '拒绝申请', description: '允许用户拒绝申请' },
                        { id: 'application_download', name: '下载附件', description: '允许用户下载申请附件' }
                    ]
                },
                {
                    id: 'schedule_management',
                    name: '生产排程管理',
                    permissions: [
                        { id: 'schedule_view', name: '查看排程', description: '允许用户查看生产排程信息' },
                        { id: 'schedule_create', name: '创建排程', description: '允许用户创建新的生产排程' },
                        { id: 'schedule_edit', name: '编辑排程', description: '允许用户编辑现有排程' },
                        { id: 'schedule_delete', name: '删除排程', description: '允许用户删除排程' },
                        { id: 'schedule_execute', name: '执行排程', description: '允许用户执行排程操作（开始、暂停、完成等）' },
                        { id: 'schedule_report', name: '排程报表', description: '允许用户查看排程分析报表' },
                        { id: 'scheduling_algorithm', name: '排程算法', description: '允许用户使用智能排程算法' },
                        { id: 'resource_manage', name: '资源管理', description: '允许用户管理生产资源（设备、人员、物料）' }
                    ]
                },
                {
                    id: 'product_management',
                    name: '产品管理',
                    permissions: [
                        { id: 'product_view', name: '查看产品', description: '允许用户查看产品信息' },
                        { id: 'product_create', name: '创建产品', description: '允许用户创建新产品' },
                        { id: 'product_edit', name: '编辑产品', description: '允许用户编辑产品信息和工艺流程' },
                        { id: 'product_delete', name: '删除产品', description: '允许用户删除产品' },
                        { id: 'operator_skill_manage', name: '操作员技能管理', description: '允许用户管理操作员技能' }
                    ]
                },
                {
                    id: 'equipment_management',
                    name: '设备管理',
                    permissions: [
                        { id: 'equipment_view', name: '查看设备', description: '允许用户查看设备信息' },
                        { id: 'equipment_create', name: '创建设备', description: '允许用户创建新设备' },
                        { id: 'equipment_edit', name: '编辑设备', description: '允许用户编辑设备信息' },
                        { id: 'equipment_delete', name: '删除设备', description: '允许用户删除设备' },
                        { id: 'equipment_maintenance', name: '设备维护', description: '允许用户管理设备维修保养记录' },
                        { id: 'equipment_health', name: '设备健康监控', description: '允许用户查看设备健康度评估' },
                        { id: 'equipment_capacity', name: '产能管理', description: '允许用户管理设备产能配置' },
                        { id: 'equipment_export', name: '导出设备数据', description: '允许用户导出设备数据' }
                    ]
                },
                {
                    id: 'maintenance_management',
                    name: '维护管理',
                    permissions: [
                        { id: 'maintenance_view', name: '查看维护记录', description: '允许用户查看维护记录' },
                        { id: 'maintenance_create', name: '创建维护记录', description: '允许用户创建维护记录' },
                        { id: 'maintenance_edit', name: '编辑维护记录', description: '允许用户编辑维护记录' },
                        { id: 'maintenance_delete', name: '删除维护记录', description: '允许用户删除维护记录' },
                        { id: 'maintenance_export', name: '导出维护记录', description: '允许用户导出维护记录' },
                        { id: 'maintenance_import', name: '导入维护记录', description: '允许用户导入维护记录' },
                        { id: 'maintenance_statistics', name: '维护统计', description: '允许用户查看维护统计数据' }
                    ]
                },
                {
                    id: 'quality_management',
                    name: '质量管理',
                    permissions: [
                        { id: 'quality_view', name: '查看质量数据', description: '允许用户查看质量检测数据' },
                        { id: 'quality_upload', name: '上传质量数据', description: '允许用户上传检测报告' },
                        { id: 'quality_edit', name: '编辑质量数据', description: '允许用户编辑质量数据' },
                        { id: 'quality_delete', name: '删除质量数据', description: '允许用户删除质量数据' },
                        { id: 'quality_download', name: '下载质量数据', description: '允许用户下载检测报告' },
                        { id: 'quality_manage', name: '质量管理', description: '允许用户管理质量相关功能' }
                    ]
                },
                {
                    id: 'file_management',
                    name: '文件管理',
                    permissions: [
                        { id: 'file_view', name: '查看文件', description: '允许用户查看文件列表和详情' },
                        { id: 'file_upload', name: '上传文件', description: '允许用户上传客户二认文件' },
                        { id: 'file_download', name: '下载文件', description: '允许用户下载文件' },
                        { id: 'file_edit', name: '编辑文件', description: '允许用户编辑文件信息' },
                        { id: 'file_delete', name: '删除文件', description: '允许用户删除文件' },
                        { id: 'file_confirm', name: '确认收到', description: '允许用户确认收到文件变更通知' },
                        { id: 'file_manage', name: '文件管理', description: '允许用户管理文件相关功能' }
                    ]
                },
                {
                    id: 'user_management',
                    name: '用户管理',
                    permissions: [
                        { id: 'user_view', name: '查看用户', description: '允许用户查看用户列表' },
                        { id: 'user_create', name: '创建用户', description: '允许用户创建新用户' },
                        { id: 'user_edit', name: '编辑用户', description: '允许用户编辑现有用户' },
                        { id: 'user_delete', name: '删除用户', description: '允许用户删除用户' },
                        { id: 'user_export', name: '导出用户', description: '允许用户导出用户数据' },
                        { id: 'user_import', name: '导入用户', description: '允许用户导入用户数据' },
                        { id: 'user_signature', name: '签名管理', description: '允许用户管理电子签名' },
                        { id: 'user_password_reset', name: '密码重置', description: '允许用户重置密码' },
                        { id: 'user_permission_manage', name: '权限管理', description: '允许用户管理其他用户的权限' }
                    ]
                },
                {
                    id: 'department_management',
                    name: '部门管理',
                    permissions: [
                        { id: 'department_view', name: '查看部门', description: '允许用户查看部门信息' },
                        { id: 'department_create', name: '创建部门', description: '允许用户创建新部门' },
                        { id: 'department_edit', name: '编辑部门', description: '允许用户编辑部门信息' },
                        { id: 'department_delete', name: '删除部门', description: '允许用户删除部门' },
                        { id: 'department_manage', name: '部门管理', description: '允许用户管理部门相关功能' }
                    ]
                },
                {
                    id: 'permission_management',
                    name: '权限管理',
                    permissions: [
                        { id: 'permission_view', name: '查看权限', description: '允许用户查看权限配置' },
                        { id: 'permission_assign', name: '分配权限', description: '允许用户分配权限' },
                        { id: 'permission_template', name: '权限模板', description: '允许用户管理权限模板' },
                        { id: 'role_manage', name: '角色管理', description: '允许用户管理用户角色' }
                    ]
                },
                {
                    id: 'system_management',
                    name: '系统管理',
                    permissions: [
                        { id: 'system_config', name: '系统配置', description: '允许用户配置系统参数' },
                        { id: 'system_monitor', name: '系统监控', description: '允许用户监控系统状态' },
                        { id: 'system_backup', name: '系统备份', description: '允许用户执行系统备份' },
                        { id: 'log_view', name: '查看日志', description: '允许用户查看系统日志' },
                        { id: 'log_download', name: '下载日志', description: '允许用户下载日志文件' },
                        { id: 'log_manage', name: '日志管理', description: '允许用户管理系统日志' }
                    ]
                },
                {
                    id: 'algorithm_tuning',
                    name: '算法调优',
                    permissions: [
                        { id: 'tuning_view', name: '查看调优参数', description: '允许用户查看算法调优参数' },
                        { id: 'tuning_edit', name: '编辑调优参数', description: '允许用户编辑调优参数' },
                        { id: 'tuning_execute', name: '执行调优', description: '允许用户执行算法调优' },
                        { id: 'algorithm_manage', name: '算法管理', description: '允许用户管理算法配置' }
                    ]
                },
                {
                    id: 'reports_statistics',
                    name: '报表统计',
                    permissions: [
                        { id: 'report_view', name: '查看报表', description: '允许用户查看各类报表' },
                        { id: 'report_create', name: '创建报表', description: '允许用户创建自定义报表' },
                        { id: 'report_export', name: '导出报表', description: '允许用户导出报表数据' },
                        { id: 'statistics_view', name: '查看统计', description: '允许用户查看统计数据' }
                    ]
                },
                {
                    id: 'general_settings',
                    name: '通用权限',
                    permissions: [
                        { id: 'user_settings', name: '个人设置', description: '允许用户修改个人设置' },
                        { id: 'dashboard_view', name: '仪表板查看', description: '允许用户查看仪表板' }
                    ]
                }
            ];

            // 提取所有权限
            availablePermissions.value = permissionGroups.value.flatMap(group => group.permissions);
        }

        // 加载用户权限
        async function loadUserPermissions() {
            if (!props.user || !props.user.id) return;

            try {
                isLoading.value = true;
                console.log('🔄 开始加载用户权限，用户ID:', props.user.id);

                const result = await getUserPermissions(props.user.id);

                if (result.success) {
                    const newPermissions = result.permissions || [];
                    console.log('✅ 权限加载成功，权限数量:', newPermissions.length);
                    console.log('📋 加载的权限:', newPermissions);

                    // 强制更新响应式数据
                    userPermissions.value.length = 0; // 清空数组
                    userPermissions.value.push(...newPermissions); // 重新填充

                    // 使用 nextTick 确保DOM更新
                    await nextTick();
                    console.log('🔄 DOM更新完成，当前权限数量:', userPermissions.value.length);
                } else {
                    console.error('❌ 加载用户权限失败:', result.message);
                }
            } catch (error) {
                console.error('❌ 加载用户权限异常:', error);
            } finally {
                isLoading.value = false;
            }
        }

        // 保存用户权限
        async function savePermissions() {
            if (!props.user || !props.user.id) return;

            try {
                isSaving.value = true;
                console.log('💾 开始保存用户权限，用户ID:', props.user.id);
                console.log('📋 保存的权限:', userPermissions.value);

                const result = await updateUserPermissions(props.user.id, userPermissions.value);

                if (result.success) {
                    console.log('✅ 权限保存成功');

                    // 添加短暂延迟确保数据库写入完成
                    await new Promise(resolve => setTimeout(resolve, 100));

                    // 重新加载用户权限以确保前端显示最新数据
                    console.log('🔄 重新加载用户权限...');
                    await loadUserPermissions();

                    console.log('🎉 权限更新流程完成');
                    alert('权限更新成功');
                    emit('updated', result.user);
                } else {
                    console.error('❌ 权限保存失败:', result.message);
                    alert('权限更新失败: ' + result.message);
                }
            } catch (error) {
                console.error('❌ 更新权限异常:', error);
                alert('更新权限失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isSaving.value = false;
            }
        }

        // 检查权限是否已分配
        function hasPermission(permissionId) {
            return userPermissions.value.includes(permissionId);
        }

        // 切换权限
        function togglePermission(permissionId) {
            if (hasPermission(permissionId)) {
                userPermissions.value = userPermissions.value.filter(id => id !== permissionId);
            } else {
                userPermissions.value.push(permissionId);
            }
        }

        // 切换组内所有权限
        function toggleGroupPermissions(groupId, enabled) {
            const group = permissionGroups.value.find(g => g.id === groupId);
            if (!group) return;

            const permissionIds = group.permissions.map(p => p.id);

            if (enabled) {
                // 添加组内所有权限
                permissionIds.forEach(id => {
                    if (!userPermissions.value.includes(id)) {
                        userPermissions.value.push(id);
                    }
                });
            } else {
                // 移除组内所有权限
                userPermissions.value = userPermissions.value.filter(id => !permissionIds.includes(id));
            }
        }

        // 检查组内所有权限是否已分配
        function isGroupFullySelected(groupId) {
            const group = permissionGroups.value.find(g => g.id === groupId);
            if (!group) return false;

            return group.permissions.every(p => hasPermission(p.id));
        }

        // 检查组内是否有部分权限已分配
        function isGroupPartiallySelected(groupId) {
            const group = permissionGroups.value.find(g => g.id === groupId);
            if (!group) return false;

            const hasAny = group.permissions.some(p => hasPermission(p.id));
            const hasAll = group.permissions.every(p => hasPermission(p.id));

            return hasAny && !hasAll;
        }

        // 检查是否可以编辑权限
        const canEditPermissions = computed(() => {
            // 只有admin角色可以编辑权限
            return props.currentUser && props.currentUser.role === 'admin';
        });

        // 批量用户相关计算属性和方法
        const filteredBatchUsers = computed(() => {
            if (!userSearchTerm.value) return batchUsers.value;

            const searchTerm = userSearchTerm.value.toLowerCase();
            return batchUsers.value.filter(user =>
                user.name.toLowerCase().includes(searchTerm) ||
                (user.role && user.role.toLowerCase().includes(searchTerm))
            );
        });

        const isAllUsersSelected = computed(() => {
            return filteredBatchUsers.value.length > 0 &&
                   filteredBatchUsers.value.every(user => isUserSelected(user.id));
        });

        // 检查用户是否被选中
        function isUserSelected(userId) {
            return selectedUsers.value.includes(userId);
        }

        // 切换用户选择状态
        function toggleUserSelection(userId) {
            if (isUserSelected(userId)) {
                selectedUsers.value = selectedUsers.value.filter(id => id !== userId);
            } else {
                selectedUsers.value.push(userId);
            }
        }

        // 切换全选/取消全选
        function toggleAllUsers() {
            if (isAllUsersSelected.value) {
                // 取消全选
                selectedUsers.value = selectedUsers.value.filter(
                    id => !filteredBatchUsers.value.some(user => user.id === id)
                );
            } else {
                // 全选
                filteredBatchUsers.value.forEach(user => {
                    if (!isUserSelected(user.id)) {
                        selectedUsers.value.push(user.id);
                    }
                });
            }
        }

        // 加载批量用户列表
        async function loadBatchUsers() {
            if (batchUsers.value.length > 0) return;

            try {
                isLoadingBatchUsers.value = true;

                // 使用getUsers API获取用户列表
                const response = await getUsers({
                    page: 1,
                    limit: 100 // 获取足够多的用户
                });

                // 过滤掉当前用户
                batchUsers.value = response.users.filter(user => user.id !== props.user.id);
            } catch (error) {
                console.error('加载用户列表失败:', error);
                alert('加载用户列表失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isLoadingBatchUsers.value = false;
            }
        }

        // 批量应用权限
        async function applyPermissionsToBatchUsers() {
            if (selectedUsers.value.length === 0) return;

            try {
                isBatchSaving.value = true;

                // 创建一个Promise数组，每个Promise代表一个用户的权限更新
                const updatePromises = selectedUsers.value.map(userId =>
                    updateUserPermissions(userId, userPermissions.value)
                );

                // 等待所有更新完成
                const results = await Promise.allSettled(updatePromises);

                // 统计成功和失败的数量
                const successCount = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
                const failCount = results.length - successCount;

                if (failCount === 0) {
                    alert(`成功更新了 ${successCount} 个用户的权限`);
                } else {
                    alert(`成功更新了 ${successCount} 个用户的权限，${failCount} 个用户更新失败`);
                }

                // 关闭模态框
                showBatchApplyModal.value = false;

                // 清空选择
                selectedUsers.value = [];

                // 通知父组件更新成功
                emit('batch-updated');
            } catch (error) {
                console.error('批量更新权限失败:', error);
                alert('批量更新权限失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isBatchSaving.value = false;
            }
        }

        // 应用权限模板
        function applyTemplate(templateId) {
            console.log('🎯 应用权限模板:', templateId);

            // 关闭下拉菜单
            showTemplateDropdown.value = false;

            // 如果是预设模板
            if (typeof templateId === 'string' && !templateId.includes('-')) {
                switch (templateId) {
                    case 'standard':
                        // 标准用户权限：新建申请、申请记录、个人设置
                        userPermissions.value = [
                            'new_application',
                            'application_record',
                            'user_settings'
                        ];
                        break;

                    case 'approver':
                        // 审批人员权限：待审核、已审核、个人设置
                        userPermissions.value = [
                            'pending_approval',
                            'approved_applications',
                            'user_settings'
                        ];
                        break;

                    case 'viewer':
                        // 只读用户权限：申请记录、已审核、个人设置
                        userPermissions.value = [
                            'application_record',
                            'approved_applications',
                            'user_settings'
                        ];
                        break;

                    case 'all':
                        // 全部权限
                        userPermissions.value = availablePermissions.value.map(p => p.id);
                        break;

                    case 'none':
                        // 清除所有权限
                        userPermissions.value = [];
                        break;
                }
            } else {
                // 如果是自定义模板
                const template = permissionTemplates.value.find(t => t.id === templateId);
                if (template) {
                    userPermissions.value = [...template.permissions];
                }
            }

            // 强制触发响应式更新
            console.log('📋 模板应用完成，当前权限数量:', userPermissions.value.length);
            nextTick(() => {
                console.log('🔄 模板应用DOM更新完成');
            });
        }

        // 监听批量应用模态框显示状态
        watch(showBatchApplyModal, (newValue) => {
            if (newValue) {
                // 当模态框显示时，加载用户列表
                loadBatchUsers();
            }
        });

        // 监听用户变化，加载权限
        watch(() => props.user, (newUser) => {
            if (newUser) {
                loadUserPermissions();
            }
        }, { immediate: true });

        // 组件挂载时加载专业模板
        onMounted(() => {
            loadProfessionalTemplates();
        });



        return {
            isLoading,
            isSaving,
            permissionGroups,
            professionalTemplates,
            hasPermission,
            togglePermission,
            toggleGroupPermissions,
            isGroupFullySelected,
            isGroupPartiallySelected,
            savePermissions,
            applyProfessionalTemplate,
            canEditPermissions,

            // 模板相关
            showTemplateDropdown,
            templateDropdownContainer,
            permissionTemplates,
            applyTemplate,

            // 批量应用相关
            showBatchDropdown,
            batchDropdownContainer,
            showBatchApplyModal,
            isLoadingBatchUsers,
            isBatchSaving,
            filteredBatchUsers,
            selectedUsers,
            userSearchTerm,
            isAllUsersSelected,
            isUserSelected,
            toggleUserSelection,
            toggleAllUsers,
            applyPermissionsToBatchUsers
        };
    },
    template: `
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-6">权限管理</h2>

            <div v-if="isLoading" class="flex justify-center items-center py-8">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span class="ml-2 text-gray-600">加载中...</span>
            </div>

            <div v-else>
                <div v-if="!canEditPermissions" class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-700">
                                您没有权限管理用户权限
                            </p>
                        </div>
                    </div>
                </div>

                <div v-else>
                    <div class="flex justify-between items-center mb-6">
                        <div class="flex items-center">
                            <button @click="$emit('back')"
                                    class="mr-3 p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors duration-200">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                                </svg>
                            </button>
                            <p class="text-gray-600">
                                为用户 <span class="font-semibold">{{ user.name }}</span> 分配可访问的页面和功能权限。
                            </p>
                        </div>

                        <div class="flex space-x-2">
                            <div class="relative" ref="templateDropdownContainer">
                                <button @click="showTemplateDropdown = !showTemplateDropdown"
                                        class="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 flex items-center">
                                    <span>应用模板</span>
                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>

                                <div v-if="showTemplateDropdown"
                                     class="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                                    <div class="py-1">
                                        <!-- 自定义权限模板 -->
                                        <div v-if="permissionTemplates.length > 0">
                                            <div class="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-200">
                                                自定义模板
                                            </div>
                                            <button v-for="template in permissionTemplates"
                                                    :key="template.id"
                                                    @click="applyTemplate(template.id)"
                                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                <div class="font-medium">{{ template.name }}</div>
                                                <div v-if="template.description" class="text-xs text-gray-500 truncate">{{ template.description }}</div>
                                            </button>
                                            <div class="border-t border-gray-200 my-1"></div>
                                        </div>

                                        <!-- 专业权限模板 -->
                                        <div v-if="professionalTemplates.length > 0">
                                            <div class="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-200">
                                                专业权限模板
                                            </div>
                                            <button v-for="template in professionalTemplates"
                                                    :key="template.id"
                                                    @click="applyProfessionalTemplate(template.id)"
                                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                <div class="font-medium">{{ template.name }}</div>
                                                <div class="text-xs text-gray-500 truncate">{{ template.description }}</div>
                                            </button>
                                            <div class="border-t border-gray-200 my-1"></div>
                                        </div>

                                        <!-- 预设模板 -->
                                        <div class="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide">
                                            预设模板
                                        </div>
                                        <button @click="applyTemplate('standard')"
                                                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <div class="font-medium">标准用户权限</div>
                                            <div class="text-xs text-gray-500 truncate">适用于普通员工的标准权限配置</div>
                                        </button>
                                        <button @click="applyTemplate('approver')"
                                                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <div class="font-medium">审批人员权限</div>
                                            <div class="text-xs text-gray-500 truncate">适用于具有审批权限的管理人员</div>
                                        </button>
                                        <button @click="applyTemplate('viewer')"
                                                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <div class="font-medium">只读用户权限</div>
                                            <div class="text-xs text-gray-500 truncate">适用于只需要查看权限的用户</div>
                                        </button>
                                        <button @click="applyTemplate('all')"
                                                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <div class="font-medium">全部权限</div>
                                            <div class="text-xs text-gray-500 truncate">授予所有系统权限（管理员级别）</div>
                                        </button>
                                        <button @click="applyTemplate('none')"
                                                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <div class="font-medium">清除所有权限</div>
                                            <div class="text-xs text-gray-500 truncate">移除用户的所有权限</div>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="relative" ref="batchDropdownContainer">
                                <button @click="showBatchDropdown = !showBatchDropdown"
                                        class="px-3 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-300 flex items-center">
                                    <span>批量应用</span>
                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>

                                <div v-if="showBatchDropdown"
                                     class="absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                                    <div class="p-3">
                                        <p class="text-sm text-gray-600 mb-2">将当前权限应用到:</p>
                                        <button @click="showBatchApplyModal = true; showBatchDropdown = false"
                                                class="w-full px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none">
                                            选择多个用户
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <div v-for="group in permissionGroups" :key="group.id" class="border border-gray-200 rounded-md p-4">
                            <div class="flex items-center mb-3">
                                <label class="inline-flex items-center cursor-pointer">
                                    <input type="checkbox"
                                           :checked="isGroupFullySelected(group.id)"
                                           :indeterminate="isGroupPartiallySelected(group.id)"
                                           @change="toggleGroupPermissions(group.id, $event.target.checked)"
                                           class="form-checkbox h-5 w-5 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                    <span class="ml-2 text-gray-700 font-medium">{{ group.name }}</span>
                                </label>
                            </div>

                            <div class="ml-6 space-y-2">
                                <div v-for="permission in group.permissions" :key="permission.id" class="flex items-start">
                                    <label class="inline-flex items-center cursor-pointer">
                                        <input type="checkbox"
                                               :checked="hasPermission(permission.id)"
                                               @change="togglePermission(permission.id)"
                                               class="form-checkbox h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                        <span class="ml-2 text-gray-700">{{ permission.name }}</span>
                                    </label>
                                    <span class="ml-2 text-xs text-gray-500">{{ permission.description }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end">
                        <button @click="savePermissions"
                                :disabled="isSaving"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50">
                            {{ isSaving ? '保存中...' : '保存权限' }}
                        </button>
                    </div>

                    <!-- 批量应用模态框 -->
                    <div v-if="showBatchApplyModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
                        <div class="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-semibold">批量应用权限</h3>
                                <button @click="showBatchApplyModal = false" class="text-gray-500 hover:text-gray-700">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>

                            <p class="text-gray-600 mb-4">
                                选择要应用相同权限的用户。这将使用当前为 <span class="font-semibold">{{ user.name }}</span> 设置的权限。
                            </p>

                            <div v-if="isLoadingBatchUsers" class="flex justify-center py-8">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                                <span class="ml-2 text-gray-600">加载中...</span>
                            </div>

                            <div v-else>
                                <div class="mb-4">
                                    <div class="flex justify-between items-center mb-2">
                                        <div class="flex items-center">
                                            <input type="checkbox"
                                                   :checked="isAllUsersSelected"
                                                   @change="toggleAllUsers"
                                                   class="form-checkbox h-5 w-5 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                            <span class="ml-2 text-gray-700 font-medium">全选/取消全选</span>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            已选择 {{ selectedUsers.length }} 个用户
                                        </div>
                                    </div>
                                    <input type="text"
                                           v-model="userSearchTerm"
                                           placeholder="搜索用户..."
                                           class="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>

                                <div class="max-h-60 overflow-y-auto border border-gray-200 rounded-md">
                                    <div v-for="batchUser in filteredBatchUsers" :key="batchUser.id"
                                         class="flex items-center p-3 hover:bg-gray-50 border-b border-gray-200 last:border-b-0">
                                        <input type="checkbox"
                                               :checked="isUserSelected(batchUser.id)"
                                               @change="toggleUserSelection(batchUser.id)"
                                               class="form-checkbox h-5 w-5 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                        <div class="ml-3">
                                            <div class="text-sm font-medium text-gray-900">{{ batchUser.name }}</div>
                                            <div class="text-xs text-gray-500">{{ batchUser.role }}</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-6 flex justify-end space-x-3">
                                    <button @click="showBatchApplyModal = false"
                                            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none">
                                        取消
                                    </button>
                                    <button @click="applyPermissionsToBatchUsers"
                                            :disabled="isBatchSaving || selectedUsers.length === 0"
                                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none disabled:opacity-50">
                                        {{ isBatchSaving ? '应用中...' : '应用权限' }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `
};
