/**
 * 权限系统测试脚本
 * 验证权限继承、管理员权限和数据一致性
 */

const { resolveCompletePermissions } = require('./backend/config/permissionInheritance');
const userService = require('./backend/services/userService');

console.log('🔍 开始权限系统测试...\n');

// 测试1: 权限继承功能
console.log('📋 测试1: 权限继承功能');
try {
    const testPermissions = ['user_edit', 'application_approve'];
    const completePermissions = resolveCompletePermissions('manager', testPermissions);
    
    console.log('输入权限:', testPermissions);
    console.log('解析后权限数量:', completePermissions.length);
    console.log('包含依赖权限:', completePermissions.includes('user_view'));
    console.log('包含组合权限:', completePermissions.includes('file_download'));
    console.log('✅ 权限继承功能正常\n');
} catch (error) {
    console.error('❌ 权限继承功能异常:', error.message);
}

// 测试2: 管理员权限
console.log('📋 测试2: 管理员权限');
try {
    const adminPermissions = resolveCompletePermissions('admin', []);
    console.log('管理员权限数量:', adminPermissions.length);
    console.log('包含用户管理权限:', adminPermissions.includes('user_permission_manage'));
    console.log('包含系统配置权限:', adminPermissions.includes('system_config'));
    console.log('包含文件管理权限:', adminPermissions.includes('file_manage'));
    
    if (adminPermissions.length >= 80) {
        console.log('✅ 管理员权限完整\n');
    } else {
        console.log('⚠️ 管理员权限可能不完整\n');
    }
} catch (error) {
    console.error('❌ 管理员权限测试异常:', error.message);
}

// 测试3: 数据库用户权限
console.log('📋 测试3: 数据库用户权限');
try {
    const users = userService.readUsers();
    const adminUsers = users.filter(user => user.role === 'admin');
    
    console.log('总用户数:', users.length);
    console.log('管理员用户数:', adminUsers.length);
    
    if (adminUsers.length > 0) {
        const adminUser = adminUsers[0];
        console.log('管理员用户:', adminUser.username);
        console.log('管理员存储权限数量:', (adminUser.permissions || []).length);
        
        const completeAdminPermissions = resolveCompletePermissions(adminUser.role, adminUser.permissions || []);
        console.log('管理员完整权限数量:', completeAdminPermissions.length);
        
        if (completeAdminPermissions.length >= 80) {
            console.log('✅ 管理员用户权限正常');
        } else {
            console.log('⚠️ 管理员用户权限需要更新');
        }
    }
    console.log('');
} catch (error) {
    console.error('❌ 数据库用户权限测试异常:', error.message);
}

// 测试4: 权限配置验证
console.log('📋 测试4: 权限配置验证');
try {
    // 测试循环依赖检测
    const testResult1 = resolveCompletePermissions('user', ['user_view']);
    console.log('普通用户权限解析正常:', testResult1.length > 0);
    
    const testResult2 = resolveCompletePermissions('manager', ['department_manage']);
    console.log('经理权限解析正常:', testResult2.length > 0);
    
    console.log('✅ 权限配置验证通过\n');
} catch (error) {
    console.error('❌ 权限配置验证失败:', error.message);
}

console.log('🎉 权限系统测试完成！');
